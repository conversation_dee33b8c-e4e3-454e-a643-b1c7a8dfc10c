#!/usr/bin/env python3
"""
Live Claude Stream Tracer - Captures real-time Claude CLI streaming outputs and traces to LangSmith.
This solves the core problem: making Claude CLI streams visible in LangSmith dashboard.
"""

import asyncio
import os
import sys
import logging
import json
import subprocess
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def live_claude_stream_tracer(prompt: str):
    """
    Run Claude CLI command and trace each streaming JSON output to Lang<PERSON>mith in real-time.
    This is exactly what your boss wants - no more black box!
    """
    logger.info("🎯 LIVE CLAUDE STREAM TRACER")
    logger.info(f"📝 Prompt: '{prompt}'")
    logger.info("🔍 Tracing each stream output to LangSmith...")
    logger.info("=" * 60)
    
    # Import our enhanced Claude tracing
    sys.path.insert(0, 'src/agents/claude_e2b')
    import claude
    
    # Create session for this Claude command
    session = claude.ClaudeSession(
        session_id=f"live-claude-{int(datetime.now().timestamp())}",
        prompt=prompt,
        trace_enabled=True
    )
    
    # Initialize LangSmith tracing
    session.init_tracing()
    logger.info(f"🔍 LangSmith tracing initialized: {session.session_id}")
    
    # The actual Claude command
    claude_command = [
        "claude", 
        "-p", prompt,
        "--output-format", "stream-json",
        "--verbose",
        "--dangerously-skip-permissions"
    ]
    
    logger.info(f"🚀 Executing: {' '.join(claude_command)}")
    
    try:
        # Run the Claude command with streaming
        process = await asyncio.create_subprocess_exec(
            *claude_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd="/Users/<USER>/backspace-mono"
        )
        
        logger.info("📡 Capturing and tracing live stream outputs...")
        
        # Read and trace each line of streaming output in real-time
        line_count = 0
        async for line in process.stdout:
            line_count += 1
            line_str = line.decode('utf-8').strip()
            
            if line_str:  # Skip empty lines
                logger.info(f"📥 Stream {line_count}: {line_str[:100]}...")
                
                # Parse and trace each real output to LangSmith
                output = claude.handle_claude_stream(line_str, session)
                
                if output:
                    logger.info(f"✅ Traced to LangSmith: {output.type}")
                    logger.info(f"   Content: {str(output.content)[:80]}...")
                    
                    # Show what will appear in LangSmith tree
                    if output.type == "system":
                        logger.info("   🌳 LangSmith: 🚀 System Init")
                    elif output.type == "claude_message":
                        logger.info("   🌳 LangSmith: 🤖 Claude Message")
                    elif output.type == "tool_call":
                        tool_name = output.content.get("name", "Unknown")
                        logger.info(f"   🌳 LangSmith: 🔧 Tool: {tool_name}")
                    elif output.type == "tool_result":
                        logger.info("   🌳 LangSmith: ✅ Tool Result")
                    elif output.type == "result":
                        logger.info("   🌳 LangSmith: 🏁 Final Result")
                else:
                    logger.warning(f"⚠️ Failed to parse: {line_str[:100]}...")
        
        # Wait for process to complete
        await process.wait()
        
        # Finalize the session
        if process.returncode == 0:
            logger.info("✅ Claude command completed successfully")
            session.finalize(success=True)
        else:
            stderr_output = await process.stderr.read()
            error_msg = stderr_output.decode('utf-8')
            logger.error(f"❌ Claude command failed: {error_msg}")
            session.finalize(success=False, error=error_msg)
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 LIVE STREAMING TRACE COMPLETED!")
        logger.info(f"📊 Session: {session.session_id}")
        logger.info(f"📝 Outputs traced: {len(session.outputs)}")
        logger.info(f"⏱️ Duration: {session.elapsed_time:.2f}s")
        logger.info(f"🔄 Return code: {process.returncode}")
        
        logger.info("\n🔍 CHECK LANGSMITH DASHBOARD:")
        logger.info("   URL: https://smith.langchain.com/")
        logger.info("   Project: backspace-testing")
        logger.info(f"   Session: {session.session_id}")
        
        logger.info("\n🌳 TREE STRUCTURE IN LANGSMITH:")
        for i, output in enumerate(session.outputs, 1):
            if output.type == "system":
                logger.info(f"   {i}. 🚀 System Init")
            elif output.type == "claude_message":
                logger.info(f"   {i}. 🤖 Claude Message")
            elif output.type == "tool_call":
                tool_name = output.content.get("name", "Unknown")
                logger.info(f"   {i}. 🔧 Tool: {tool_name}")
            elif output.type == "tool_result":
                logger.info(f"   {i}. ✅ Tool Result")
            elif output.type == "result":
                logger.info(f"   {i}. 🏁 Final Result")
        
        return session
        
    except FileNotFoundError:
        logger.error("❌ 'claude' command not found!")
        logger.error("Make sure Claude CLI is installed and in PATH")
        session.finalize(success=False, error="Claude CLI not found")
        return session
    except Exception as e:
        logger.error(f"❌ Error running Claude command: {e}")
        session.finalize(success=False, error=str(e))
        return session

def check_environment():
    """Check if all required environment variables are set."""
    logger.info("🔍 Checking environment setup...")
    
    required_vars = {
        "LANGCHAIN_API_KEY": "LangSmith API key",
        "ANTHROPIC_API_KEY": "Anthropic Claude API key"
    }
    
    missing = []
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET ({desc})")
            missing.append(var)
    
    # Check LangSmith settings
    tracing = os.getenv("LANGCHAIN_TRACING_V2", "false")
    project = os.getenv("LANGCHAIN_PROJECT", "default")
    
    logger.info(f"   🔍 LANGCHAIN_TRACING_V2: {tracing}")
    logger.info(f"   📊 LANGCHAIN_PROJECT: {project}")
    
    if missing:
        logger.error(f"❌ Missing required environment variables: {missing}")
        return False
    
    logger.info("✅ Environment check passed!")
    return True

async def main():
    """Main function to run live Claude streaming with tracing."""
    logger.info("🎯 LIVE CLAUDE STREAM TRACER")
    logger.info("Goal: Capture LIVE Claude CLI streams and trace to LangSmith")
    logger.info("Solving: No more black box - complete visibility!")
    logger.info("=" * 70)
    
    # Check environment
    if not check_environment():
        logger.error("❌ Environment check failed!")
        return
    
    # Test with the same command your boss wants to trace
    prompt = "list the files in current directory"
    
    try:
        session = await live_claude_stream_tracer(prompt)
        
        if session:
            logger.info("\n" + "=" * 70)
            logger.info("🎉 SUCCESS: LIVE STREAMING TRACE WORKING!")
            logger.info("")
            logger.info("✅ Problem SOLVED:")
            logger.info("   - Claude CLI streams are now captured")
            logger.info("   - Each JSON output traced to LangSmith")
            logger.info("   - Tree visualization available")
            logger.info("   - Real-time monitoring enabled")
            logger.info("   - No more black box!")
            logger.info("")
            logger.info("🔍 Your boss can now see:")
            logger.info("   - Complete Claude conversation flow")
            logger.info("   - Tool usage (LS, Bash, etc.)")
            logger.info("   - Real-time execution steps")
            logger.info("   - Cost and timing data")
        else:
            logger.error("❌ Streaming trace failed")
        
    except Exception as e:
        logger.error(f"❌ Main execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
