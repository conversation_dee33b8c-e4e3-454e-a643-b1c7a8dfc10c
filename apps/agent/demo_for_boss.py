#!/usr/bin/env python3
"""
Demo for boss showing Claude Code with E2B and Lang<PERSON>mith tracing.
This demonstrates the exact solution to replace the black box approach.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Set up environment
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_with_real_e2b():
    """Demo with real E2B sandbox showing the tracing solution."""
    logger.info("🎯 DEMO FOR BOSS: Claude Code + E2B + LangSmith Tracing")
    logger.info("=" * 70)
    
    try:
        # Import the enhanced Claude module
        from agents.claude_e2b.claude import run_claude_in_sandbox
        from e2b_code_interpreter import AsyncSandbox
        
        logger.info("🔧 Creating E2B sandbox...")
        
        # Create E2B sandbox (this replaces the black box)
        sandbox = await AsyncSandbox.create()
        
        logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
        
        try:
            # This is the prompt your boss wants to trace
            prompt = "do ls"  # Same as: claude -p "do ls" --output-format stream-json
            
            logger.info("🤖 Running Claude Code with tracing...")
            logger.info(f"📝 Prompt: '{prompt}'")
            logger.info("🔍 Tracing: ENABLED (will send to LangSmith)")
            
            # BEFORE (black box): run_claude_sandbox(prompt)
            # AFTER (with tracing): run_claude_in_sandbox with enable_tracing=True
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,  # ← This is the key change!
                timeout=60
            )
            
            logger.info("📊 RESULTS:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
            logger.info(f"   📝 Stream outputs captured: {len(session.outputs)}")
            if session.total_cost_usd:
                logger.info(f"   💰 Cost: ${session.total_cost_usd:.4f}")
            
            logger.info("\n🔍 TRACED OUTPUTS (what boss can now see):")
            for i, output in enumerate(session.outputs, 1):
                logger.info(f"   {i}. {output.type}: {str(output.content)[:80]}...")
            
            return session
            
        finally:
            # Clean up sandbox
            await sandbox.close()
            logger.info("🧹 E2B sandbox closed")
            
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def demo_streaming_with_e2b():
    """Demo streaming approach with E2B."""
    logger.info("\n" + "=" * 70)
    logger.info("🌊 STREAMING DEMO: Real-time tracing")
    
    try:
        from agents.claude_e2b.claude import stream_claude_in_sandbox
        from e2b_code_interpreter import AsyncSandbox
        
        sandbox = await AsyncSandbox.create()
        
        logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
        
        try:
            prompt = "create a simple python hello world script and run it"
            
            logger.info("🤖 Streaming Claude Code with tracing...")
            logger.info(f"📝 Prompt: '{prompt}'")
            
            outputs = []
            async for output in stream_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,  # Real-time tracing
                timeout=90
            ):
                outputs.append(output)
                logger.info(f"📡 LIVE: {output.type} - {str(output.content)[:60]}...")
            
            logger.info(f"📊 Streaming completed: {len(outputs)} outputs traced")
            return outputs
            
        finally:
            await sandbox.close()
            logger.info("🧹 E2B sandbox closed")
            
    except Exception as e:
        logger.error(f"❌ Streaming demo failed: {e}")
        return None

def explain_to_boss():
    """Explain the solution to the boss."""
    logger.info("\n" + "=" * 70)
    logger.info("📋 EXPLANATION FOR BOSS")
    logger.info("=" * 70)
    
    logger.info("🔴 BEFORE (Black Box Problem):")
    logger.info("   - run_claude_sandbox(prompt) → No visibility")
    logger.info("   - Could not see Claude thinking process")
    logger.info("   - No tool usage tracking")
    logger.info("   - No real-time monitoring")
    
    logger.info("\n🟢 AFTER (With Tracing Solution):")
    logger.info("   - run_claude_in_sandbox(prompt, enable_tracing=True)")
    logger.info("   - Every stream output captured and traced")
    logger.info("   - Real-time visibility in LangSmith")
    logger.info("   - Tree structure showing message flow")
    
    logger.info("\n🔧 TECHNICAL CHANGES:")
    logger.info("   1. Added LangSmith client integration")
    logger.info("   2. Enhanced ClaudeSession with tracing methods")
    logger.info("   3. Added enable_tracing parameter to functions")
    logger.info("   4. Intercept each JSON stream output")
    logger.info("   5. Send structured traces to LangSmith")
    
    logger.info("\n🎯 WHAT BOSS CAN NOW SEE:")
    logger.info("   - User input")
    logger.info("   - Claude messages")
    logger.info("   - Tool calls (Bash, Read, Write, etc.)")
    logger.info("   - Tool results")
    logger.info("   - Timing and cost data")
    logger.info("   - Complete conversation context")
    
    logger.info("\n🔍 WHERE TO SEE IT:")
    logger.info("   - Go to: https://smith.langchain.com/")
    logger.info("   - Project: 'backspace-testing'")
    logger.info("   - Look for 'claude_code_session' traces")

async def main():
    """Main demo for boss."""
    # Set up tracing environment
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
    
    # Check environment
    api_key = os.getenv("LANGCHAIN_API_KEY")
    e2b_key = os.getenv("E2B_API_KEY")
    
    if not api_key or not e2b_key:
        logger.error("❌ Missing API keys!")
        logger.error(f"   LANGCHAIN_API_KEY: {'SET' if api_key else 'NOT SET'}")
        logger.error(f"   E2B_API_KEY: {'SET' if e2b_key else 'NOT SET'}")
        return
    
    logger.info("✅ Environment ready:")
    logger.info(f"   🔑 LangSmith: {api_key[:20]}...")
    logger.info(f"   🔑 E2B: {e2b_key[:20]}...")
    
    # Run demos
    session = await demo_with_real_e2b()
    
    if session:
        outputs = await demo_streaming_with_e2b()
    
    # Explain the solution
    explain_to_boss()
    
    logger.info("\n" + "=" * 70)
    logger.info("🎉 DEMO COMPLETE!")
    logger.info("🔍 Boss can now check LangSmith dashboard:")
    logger.info("   https://smith.langchain.com/")
    logger.info("   Project: backspace-testing")

if __name__ == "__main__":
    asyncio.run(main())
