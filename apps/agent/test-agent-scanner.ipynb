import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Cell 1: Setup Python path and environment
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, '/Users/<USER>/backspace-mono/apps/agent/src')

# Change working directory to the agent folder
os.chdir('/Users/<USER>/backspace-mono/apps/agent')

print("✅ Python path updated")
print(f"✅ Working directory: {os.getcwd()}")

# Cell 2: Load environment variables and enable tracing
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('src/agents/.env')

# Set LangSmith tracing (note: LangSmith client expects LANGCHAIN_* variables)
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_TRACING_V2"] = os.getenv("LANGSMITH_TRACING", "true")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "backspace-testing")

# Disable automatic test run creation to avoid "Test Run" entries
os.environ["LANGCHAIN_TEST_CACHE"] = "false"

print("✅ Environment variables loaded")
print(f"✅ LangSmith API Key: {os.environ.get('LANGCHAIN_API_KEY', 'NOT SET')[:20]}...")
print(f"✅ LangSmith Project: {os.environ.get('LANGCHAIN_PROJECT', 'NOT SET')}")
print("✅ LangSmith tracing enabled")

# Cell 4: Import and setup
from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()
print("✅ Database connected")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

from agents.claude_scanner.deployment import agent, graph
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_scanner.states import ClaudeScannerState, MetricType

# Your custom configuration with proper MetricType enum
initial_state: ClaudeScannerState = {
    "sandbox": None,
    "repo_path": "/Users/<USER>/backspace/backspace-monorepo/apps/web",
    "analysis_results": [],
    "aggregate_result": None,
    "error": None,
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


# Cell 1: Test run_claude_in_sandbox (waits for completion, returns full session)
import asyncio
from agents.claude_e2b import create_sandbox, cleanup_sandbox, run_claude_in_sandbox
from db import db_manager


# Connect to database
await db_manager.connect()

# Create sandbox
sandbox = await create_sandbox()

result = await sandbox.commands.run("cd workspace && git show-branch main roland/hooks")
print(result.stdout)