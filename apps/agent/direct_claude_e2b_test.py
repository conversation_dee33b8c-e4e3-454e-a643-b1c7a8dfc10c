#!/usr/bin/env python3
"""
Direct test of Claude E2B integration with LangSmith tracing.
This bypasses the agent system and directly tests the core functionality.
"""

import asyncio
import os
import sys
import logging
import json
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_direct_claude_e2b():
    """
    Direct test of Claude E2B with tracing - bypassing agent system.
    """
    logger.info("🎯 DIRECT CLAUDE E2B + LANGSMITH TRACING TEST")
    logger.info("=" * 60)
    
    try:
        # Import E2B
        from e2b_code_interpreter import AsyncSandbox
        
        # Import our enhanced Claude classes directly
        sys.path.insert(0, 'src')
        
        # Import only the classes we need, not the full agent system
        from agents.claude_e2b.claude import ClaudeSession, ClaudeOutput, handle_claude_stream
        
        logger.info("🔧 Creating E2B sandbox...")
        
        # Create E2B sandbox
        async with AsyncSandbox() as sandbox:
            logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
            
            # Create a session with tracing
            session = ClaudeSession(
                session_id=f"direct-e2b-{int(datetime.now().timestamp())}",
                prompt="do ls",
                trace_enabled=True
            )
            
            # Initialize LangSmith tracing
            session.init_tracing()
            logger.info("🔍 LangSmith tracing initialized")
            
            # Simulate running Claude command in E2B and getting stream outputs
            # This simulates what would happen when Claude runs in the sandbox
            logger.info("🤖 Simulating Claude execution in E2B...")
            
            # These are the types of outputs we'd get from Claude in E2B
            simulated_outputs = [
                {
                    "type": "system",
                    "subtype": "init",
                    "cwd": f"/tmp/{sandbox.sandbox_id}",
                    "session_id": session.session_id,
                    "model": "claude-3-sonnet",
                    "tools": ["Bash", "Read", "Write", "LS"]
                },
                {
                    "type": "assistant",
                    "message": {
                        "content": [{"type": "text", "text": "I'll list the files in the current directory using the ls command."}]
                    }
                },
                {
                    "type": "assistant", 
                    "message": {
                        "content": [{"type": "tool_use", "id": "tool_1", "name": "Bash", "input": {"command": "ls -la"}}]
                    }
                },
                {
                    "type": "user",
                    "message": {
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": "tool_1",
                            "content": "total 8\ndrwxr-xr-x 2 <USER> <GROUP> 4096 Jan 1 12:00 .\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 ..\n-rw-r--r-- 1 <USER> <GROUP>   42 Jan 1 12:00 README.md",
                            "is_error": False
                        }]
                    }
                }
            ]
            
            # Process each output through our tracing system
            for i, output_data in enumerate(simulated_outputs, 1):
                logger.info(f"📡 Processing E2B output {i}/{len(simulated_outputs)}")
                
                # Convert to JSON string (as it would come from Claude)
                json_line = json.dumps(output_data)
                
                # Parse and trace the output
                output = handle_claude_stream(json_line, session)
                
                if output:
                    logger.info(f"✅ Traced: {output.type}")
                    logger.info(f"   Content: {str(output.content)[:80]}...")
                else:
                    logger.warning(f"⚠️ Failed to parse output")
            
            # Finalize the session
            session.finalize(success=True)
            
            logger.info("📊 DIRECT E2B RESULTS:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   📝 Outputs traced: {len(session.outputs)}")
            logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
            logger.info(f"   🆔 Session ID: {session.session_id}")
            
            return session
            
    except Exception as e:
        logger.error(f"❌ Direct test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_real_e2b_command():
    """
    Test running an actual command in E2B sandbox.
    """
    logger.info("\n" + "=" * 60)
    logger.info("🔧 TESTING REAL E2B COMMAND EXECUTION")
    
    try:
        from e2b_code_interpreter import AsyncSandbox
        
        async with AsyncSandbox() as sandbox:
            logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
            
            # Run a real command in the sandbox
            logger.info("🚀 Running 'ls -la' in E2B sandbox...")
            
            result = await sandbox.run_code("import os; print(os.listdir('.'))")
            
            logger.info("📊 E2B Command Results:")
            logger.info(f"   📝 Output: {result.text}")
            logger.info(f"   ✅ Success: {not result.error}")
            if result.error:
                logger.error(f"   ❌ Error: {result.error}")
            
            # Create a trace for this real execution
            sys.path.insert(0, 'src')
            from agents.claude_e2b.claude import ClaudeSession
            
            session = ClaudeSession(
                session_id=f"real-e2b-{int(datetime.now().timestamp())}",
                prompt="list directory contents",
                trace_enabled=True
            )
            
            session.init_tracing()
            
            # Create a manual output for the real execution
            from agents.claude_e2b.claude import ClaudeOutput
            
            output = ClaudeOutput(
                timestamp=datetime.now().timestamp(),
                type="tool_result",
                content={
                    "command": "os.listdir('.')",
                    "result": result.text,
                    "is_error": bool(result.error),
                    "sandbox_id": sandbox.sandbox_id
                }
            )
            
            session.add_output(output)
            session.finalize(success=not result.error)
            
            logger.info(f"🔍 Real E2B execution traced to LangSmith: {session.session_id}")
            
            return session
            
    except Exception as e:
        logger.error(f"❌ Real E2B test failed: {e}")
        return None

def check_environment():
    """Check environment setup."""
    logger.info("🔍 Checking environment...")
    
    required_vars = ["LANGCHAIN_API_KEY", "E2B_API_KEY", "ANTHROPIC_API_KEY"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET")
            return False
    
    logger.info("✅ Environment ready!")
    return True

async def main():
    """Main test function."""
    logger.info("🎯 DIRECT CLAUDE E2B + LANGSMITH INTEGRATION TEST")
    logger.info("Goal: Test core tracing functionality without agent system")
    logger.info("=" * 70)
    
    if not check_environment():
        return
    
    try:
        # Test 1: Direct Claude E2B tracing
        session1 = await test_direct_claude_e2b()
        
        # Test 2: Real E2B command execution
        session2 = await test_real_e2b_command()
        
        logger.info("\n" + "=" * 70)
        logger.info("🎉 DIRECT TESTING COMPLETED!")
        
        if session1 or session2:
            logger.info("✅ SUCCESS: Core tracing functionality is working!")
            logger.info("🔍 Check LangSmith dashboard:")
            logger.info("   https://smith.langchain.com/")
            logger.info("   Project: backspace-testing")
            if session1:
                logger.info(f"   Session 1: {session1.session_id}")
            if session2:
                logger.info(f"   Session 2: {session2.session_id}")
            
            logger.info("\n📊 What your boss will see:")
            logger.info("   - E2B sandbox execution traces")
            logger.info("   - Claude command processing")
            logger.info("   - Tool usage and results")
            logger.info("   - Complete conversation flow")
        else:
            logger.error("❌ All tests failed")
        
    except Exception as e:
        logger.error(f"❌ Main test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
