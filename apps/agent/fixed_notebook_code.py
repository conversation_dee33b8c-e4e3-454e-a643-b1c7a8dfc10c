#!/usr/bin/env python3
"""
FIXED Notebook Code - Uses same approach as working direct_claude_cli_tracer.py
Copy these cells to your Jupyter notebook for working child node tracing
"""

print("📝 FIXED NOTEBOOK CODE - COPY THESE CELLS:")
print("=" * 80)

print("""
# Cell 1: Environment Setup (CRITICAL - Run this FIRST)
import os
import sys
import logging
from datetime import datetime

# Set ALL environment variables BEFORE imports
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"

print("✅ Environment variables set")

# Cell 2: Import Modules (NO MONKEY PATCHING)
# Use SAME import approach as working script
sys.path.insert(0, 'src/agents/claude_e2b')
import claude
import e2b_sandbox

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s  %(message)s")
claude.logger.setLevel(logging.DEBUG)

print("✅ Modules imported - NO monkey patching needed!")

# Cell 3: Create Sandbox with Database Connection
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Connect to database (required for E2B)
sys.path.insert(0, 'src')
from db import db_manager
await db_manager.connect()
print("✅ Database connected")

sandbox = await e2b_sandbox.create_sandbox(
    envs={"ANTHROPIC_API_KEY": os.environ["ANTHROPIC_API_KEY"]},
    timeout=120
)
print(f"✅ Sandbox created: {sandbox.sandbox_id}")

# Cell 4: Stream with WORKING Hierarchical Tracing
prompt = "do ls"
print("🚀 Starting Claude session with WORKING hierarchical tracing...")
print("📍 Check LangSmith NOW to see child nodes appearing!")
print("   URL: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("-" * 80)

session_id = None
output_count = 0

async for out in claude.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "10"},
    timeout=60,
    enable_tracing=True  # ← This should now work with child nodes!
):
    output_count += 1
    
    # Extract session ID from first output
    if not session_id and hasattr(out, 'raw_event'):
        event = out.raw_event or {}
        session_id = event.get('session_id', 'unknown')
        print(f"📋 Session ID: {session_id}")
    
    # Display output with more detail
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")
    
    # The hierarchical trace should now be building in LangSmith in real-time!
    # You should see: 🚀 System Init, 🔧 Tool calls, ✅ Results, 🤖 Claude Messages

print(f"\\n=== STREAM COMPLETE ===")
print(f"✅ Total outputs: {output_count}")
print(f"🔍 Check LangSmith for session: {session_id}")
print("📍 URL: https://smith.langchain.com/")
print("📂 Project: backspace-testing")
print("🌳 You should now see CHILD NODES in the tree!")

# Cell 5: Cleanup
await sandbox.close()
print("✅ Sandbox closed")
""")

print("\n" + "=" * 80)
print("🔧 KEY FIXES:")
print("1. ❌ REMOVED monkey patching that was breaking tracing")
print("2. ✅ Using SAME import approach as working script")
print("3. ✅ Added database connection (required for E2B)")
print("4. ✅ Using direct claude module import")
print("5. ✅ Child nodes should now appear in LangSmith!")
print("\n🎯 This should fix the black box issue and show child nodes!")
