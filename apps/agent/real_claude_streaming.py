#!/usr/bin/env python3
"""
REAL Claude streaming integration with LangSmith tracing.
This runs the ACTUAL command and captures live stream outputs:
claude -p "do ls" --output-format stream-json --verbose --dangerously-skip-permissions
"""

import asyncio
import os
import sys
import logging
import json
import subprocess
from datetime import datetime

# Set up environment
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_real_claude_command_with_tracing():
    """
    Run the REAL Claude command and capture live streaming outputs:
    claude -p "do ls" --output-format stream-json --verbose --dangerously-skip-permissions
    """
    logger.info("🎯 RUNNING REAL CLAUDE COMMAND WITH LIVE STREAMING")
    logger.info("Command: claude -p \"do ls\" --output-format stream-json --verbose --dangerously-skip-permissions")
    logger.info("=" * 80)
    
    # Import our enhanced tracing
    from agents.claude_e2b.claude import ClaudeSession, handle_claude_stream
    
    # Create session for real command
    session = ClaudeSession(
        session_id=f"real-claude-{int(datetime.now().timestamp())}",
        prompt="do ls",
        trace_enabled=True
    )
    
    # Initialize LangSmith tracing
    session.init_tracing()
    logger.info("🔍 LangSmith tracing initialized")
    
    # The actual Claude command
    claude_command = [
        "claude", 
        "-p", "do ls",
        "--output-format", "stream-json",
        "--verbose",
        "--dangerously-skip-permissions"
    ]
    
    logger.info(f"🚀 Executing: {' '.join(claude_command)}")
    
    try:
        # Run the real Claude command
        process = await asyncio.create_subprocess_exec(
            *claude_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd="/Users/<USER>/Downloads/backspace-mono-main"  # Same directory as your boss's example
        )
        
        logger.info("📡 Capturing live stream outputs...")
        
        # Read and process each line of streaming output
        line_count = 0
        async for line in process.stdout:
            line_count += 1
            line_str = line.decode('utf-8').strip()
            
            if line_str:  # Skip empty lines
                logger.info(f"📥 Stream output {line_count}: {line_str[:100]}...")
                
                # Parse and trace each real output
                output = handle_claude_stream(line_str, session)
                
                if output:
                    logger.info(f"✅ Traced: {output.type}")
                    logger.info(f"   Content: {str(output.content)[:80]}...")
                else:
                    logger.warning(f"⚠️ Failed to parse: {line_str[:100]}...")
        
        # Wait for process to complete
        await process.wait()
        
        # Check if command was successful
        if process.returncode == 0:
            logger.info("✅ Claude command completed successfully")
            session.finalize(success=True)
        else:
            stderr_output = await process.stderr.read()
            error_msg = stderr_output.decode('utf-8')
            logger.error(f"❌ Claude command failed: {error_msg}")
            session.finalize(success=False, error=error_msg)
        
        logger.info("📊 REAL STREAMING RESULTS:")
        logger.info(f"   ✅ Success: {session.success}")
        logger.info(f"   📝 Live outputs traced: {len(session.outputs)}")
        logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
        logger.info(f"   🔄 Return code: {process.returncode}")
        
        return session
        
    except FileNotFoundError:
        logger.error("❌ 'claude' command not found!")
        logger.error("Make sure Claude CLI is installed and in PATH")
        session.finalize(success=False, error="Claude CLI not found")
        return session
    except Exception as e:
        logger.error(f"❌ Error running Claude command: {e}")
        session.finalize(success=False, error=str(e))
        return session

async def run_with_e2b_sandbox():
    """
    Alternative: Run Claude within E2B sandbox (if direct CLI doesn't work)
    """
    logger.info("🔄 ALTERNATIVE: Running Claude within E2B sandbox")
    
    try:
        from agents.claude_e2b.claude import run_claude_in_sandbox
        from e2b_code_interpreter import AsyncSandbox
        
        async with AsyncSandbox() as sandbox:
            logger.info(f"📦 E2B sandbox created: {sandbox.sandbox_id}")
            
            # Run the same command within E2B
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt="do ls",
                claude_options={
                    "output_format": "stream-json",
                    "verbose": True,
                    "dangerously_skip_permissions": True
                },
                enable_tracing=True,
                timeout=60
            )
            
            logger.info("📊 E2B SANDBOX RESULTS:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   📝 Outputs traced: {len(session.outputs)}")
            logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
            
            return session
            
    except Exception as e:
        logger.error(f"❌ E2B sandbox approach failed: {e}")
        return None

def check_claude_cli():
    """Check if Claude CLI is available."""
    try:
        result = subprocess.run(["claude", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ Claude CLI found: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Claude CLI not working properly")
            return False
    except FileNotFoundError:
        logger.error("❌ Claude CLI not found in PATH")
        return False

async def main():
    """Main function to run real Claude streaming with tracing."""
    logger.info("🎯 REAL CLAUDE STREAMING WITH LANGSMITH TRACING")
    logger.info("Goal: Capture LIVE outputs from actual Claude command")
    logger.info("=" * 80)
    
    # Check environment
    api_key = os.getenv("LANGCHAIN_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    if not api_key:
        logger.error("❌ LANGCHAIN_API_KEY not set!")
        return
    
    if not anthropic_key:
        logger.error("❌ ANTHROPIC_API_KEY not set!")
        return
    
    logger.info("✅ Environment ready:")
    logger.info(f"   🔑 LangSmith API Key: {api_key[:20]}...")
    logger.info(f"   🔑 Anthropic API Key: {anthropic_key[:20]}...")
    logger.info(f"   📊 Project: {os.getenv('LANGCHAIN_PROJECT')}")
    
    # Check if Claude CLI is available
    if check_claude_cli():
        logger.info("🚀 Using direct Claude CLI approach")
        session = await run_real_claude_command_with_tracing()
    else:
        logger.info("🔄 Falling back to E2B sandbox approach")
        session = await run_with_e2b_sandbox()
    
    if session:
        logger.info("\n" + "=" * 80)
        logger.info("🎉 REAL STREAMING COMPLETED!")
        logger.info("🔍 Your boss can check: https://smith.langchain.com/")
        logger.info("📊 Project: backspace-testing")
        logger.info(f"🆔 Session ID: {session.session_id}")
        logger.info("")
        logger.info("💡 This is now REAL streaming, not hardcoded demo!")
        logger.info("   Every JSON output came from the actual Claude command")
    else:
        logger.error("❌ Both approaches failed")

if __name__ == "__main__":
    asyncio.run(main())
