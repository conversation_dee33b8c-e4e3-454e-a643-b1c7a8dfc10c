"""Claude Code integration utilities for E2B sandbox."""

import json
import logging
import asyncio
import os
from typing import Optional, List, Dict, Any, AsyncIterator, Callable
from dataclasses import dataclass, field
from datetime import datetime

from e2b_code_interpreter import AsyncSandbox
from langsmith import Client, traceable
from langsmith.run_trees import RunTree

# Configure logger with emoji support
logger = logging.getLogger(__name__)

# Initialize LangSmith client
_langsmith_client: Optional[Client] = None

def get_langsmith_client() -> Optional[Client]:
    """Get the LangSmith client, initializing it if necessary (lazy initialization)."""
    global _langsmith_client
    if _langsmith_client is None:
        api_key = os.getenv("LANGCHAIN_API_KEY")
        if api_key:
            logger.info("🔑 LangSmith API key found, initializing client.")
            _langsmith_client = Client(
                api_key=api_key,
                api_url=os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")
            )
        else:
            logger.warning("⚠️ LANGCHAIN_API_KEY not set, LangSmith client will not be initialized.")
    return _langsmith_client


@dataclass
class ClaudeOutput:
    """Container for storing Claude Code outputs."""
    timestamp: float
    type: str
    content: Any
    raw_event: Optional[Dict[str, Any]] = None
    
    def __str__(self):
        return f"[{self.timestamp:.2f}] {self.type}: {self.content}"


@dataclass
class ClaudeSession:
    """Container for a complete Claude Code session."""
    session_id: str
    prompt: str
    outputs: List[ClaudeOutput] = field(default_factory=list)
    start_time: float = field(default_factory=lambda: datetime.now().timestamp())
    end_time: Optional[float] = None
    total_cost_usd: Optional[float] = None
    duration_ms: Optional[int] = None
    success: bool = False
    error: Optional[str] = None
    # LangSmith tracing
    run_tree: Optional['RunTree'] = field(default=None, init=False)
    trace_enabled: bool = field(default=True)
    child_runs: List['RunTree'] = field(default_factory=list, init=False)
    pending_tools: Dict[str, 'RunTree'] = field(default_factory=dict, init=False)
    
    def add_output(self, output: ClaudeOutput):
        """Add an output to the session."""
        self.outputs.append(output)
        # Send to LangSmith if tracing is enabled
        if self.trace_enabled and self.run_tree and get_langsmith_client():
            logger.info(f"🔍 Tracing output: {output.type} to LangSmith")
            self._trace_output(output)
        else:
            logger.warning(f"⚠️ Tracing disabled: trace_enabled={self.trace_enabled}, run_tree={self.run_tree is not None}, client={get_langsmith_client() is not None}")

    def finalize(self, success: bool = True, error: Optional[str] = None):
        """Mark the session as complete."""
        self.end_time = datetime.now().timestamp()
        self.success = success
        self.error = error

        # Clean up any pending tools (tools without results)
        if self.pending_tools:
            logger.warning(f"⚠️ Finalizing {len(self.pending_tools)} pending tools without results")
            for tool_id, tool_run in self.pending_tools.items():
                try:
                    tool_run.end()
                    tool_run.patch()
                except Exception as e:
                    logger.warning(f"⚠️ Failed to finalize pending tool {tool_id}: {e}")
            self.pending_tools.clear()

        # Finalize LangSmith trace
        if self.trace_enabled and self.run_tree and get_langsmith_client():
            self._finalize_trace()

    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds."""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now().timestamp() - self.start_time

    def init_tracing(self, parent_run: Optional['RunTree'] = None):
        """Initialize LangSmith tracing for this session."""
        client = get_langsmith_client()
        if not client or not self.trace_enabled:
            return

        try:
            from langsmith.run_trees import RunTree

            self.run_tree = RunTree(
                name="🤖 Claude Code Session",
                run_type="chain",
                inputs={
                    "prompt": self.prompt,
                    "session_id": self.session_id,
                    "timestamp": datetime.now().isoformat()
                },
                client=client,
                parent_run=parent_run
            )
            self.run_tree.post()
            logger.info(f"🔍 LangSmith tracing initialized for session: {self.session_id}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LangSmith tracing: {e}")
            self.trace_enabled = False

    def _trace_output(self, output: ClaudeOutput):
        """Send output to LangSmith trace with hierarchical structure and proper tool-result nesting."""
        if not self.run_tree:
            return

        try:
            # Handle tool calls - create as parent nodes and store for result matching
            if output.type == "tool_call":
                if isinstance(output.content, dict):
                    tool_name = output.content.get("name", "Unknown")
                    tool_id = output.content.get("id")

                    # Create tool call as parent node
                    tool_run = self.run_tree.create_child(
                        name=f"🔧 Tool: {tool_name}",
                        run_type="tool",
                        inputs={
                            "tool_name": tool_name,
                            "tool_input": output.content.get("input"),
                            "tool_id": tool_id,
                            "timestamp": output.timestamp
                        },
                        extra={
                            "description": f"Calling {tool_name}",
                            "raw_event": output.raw_event
                        }
                    )
                    tool_run.post()

                    # Store the tool run for later result matching
                    if tool_id:
                        self.pending_tools[tool_id] = tool_run

                    self.child_runs.append(tool_run)
                    logger.info(f"📤 Created tool call: 🔧 Tool: {tool_name} (ID: {tool_id})")
                    return

            # Handle tool results - create as children of their respective tools
            elif output.type == "tool_result":
                if isinstance(output.content, dict):
                    tool_use_id = output.content.get("tool_use_id")
                    is_error = output.content.get("is_error", False)

                    # Find the matching tool call
                    parent_tool = self.pending_tools.get(tool_use_id)

                    if parent_tool:
                        # Create result as CHILD of the tool
                        result_run = parent_tool.create_child(
                            name="✅ Result" if not is_error else "❌ Error",
                            run_type="chain",
                            outputs={
                                "result": output.content.get("content"),
                                "is_error": is_error,
                                "tool_use_id": tool_use_id
                            },
                            extra={
                                "description": "Tool execution result",
                                "raw_event": output.raw_event
                            }
                        )
                        result_run.post()
                        result_run.end()
                        result_run.patch()

                        # End the parent tool too
                        parent_tool.end()
                        parent_tool.patch()

                        # Remove from pending tools
                        del self.pending_tools[tool_use_id]

                        logger.info(f"📤 Created nested result: {'✅' if not is_error else '❌'} Result under tool {tool_use_id}")
                        return
                    else:
                        logger.warning(f"⚠️ No matching tool found for result {tool_use_id}")

            # Handle other output types (system, claude_message, etc.) as before
            run_configs = {
                "system": {
                    "name": "🚀 System Init",
                    "run_type": "chain",
                    "description": "Claude system initialization"
                },
                "claude_message": {
                    "name": "🤖 Claude Message",
                    "run_type": "llm",
                    "description": "Claude's reasoning and response"
                },
                "thinking": {
                    "name": "🧠 Claude Thinking",
                    "run_type": "llm",
                    "description": "Claude's internal reasoning process"
                },
                "error": {
                    "name": "💥 Error",
                    "run_type": "chain",
                    "description": "Error occurred"
                },
                "result": {
                    "name": "📊 Final Result",
                    "run_type": "chain",
                    "description": "Session completion summary"
                }
            }

            config = run_configs.get(output.type, {
                "name": f"📝 {output.type}",
                "run_type": "chain",
                "description": output.type
            })

            # Create more detailed inputs/outputs for better visibility
            inputs = {
                "type": output.type,
                "timestamp": output.timestamp
            }

            outputs = {"content": output.content}

            # Add extra context for specific types
            if output.type == "result":
                if isinstance(output.content, dict):
                    outputs["duration_ms"] = output.content.get("duration_ms")
                    outputs["cost_usd"] = output.content.get("total_cost_usd")
                    outputs["success"] = not output.content.get("is_error")

            # Create child run with enhanced metadata
            child_run = self.run_tree.create_child(
                name=config["name"],
                run_type=config["run_type"],
                inputs=inputs,
                outputs=outputs,
                extra={
                    "description": config["description"],
                    "raw_event": output.raw_event
                }
            )
            child_run.post()

            # For real-time visibility, end and patch the child run immediately
            child_run.end()
            child_run.patch()

            # Still store it for reference
            self.child_runs.append(child_run)

            logger.info(f"📤 Created child run in LangSmith: {config['name']} (Total child runs: {len(self.child_runs)})")

        except Exception as e:
            logger.warning(f"⚠️ Failed to trace output: {e}")

    def _finalize_trace(self):
        """Finalize the LangSmith trace."""
        if not self.run_tree:
            return

        try:
            # Child runs are already ended in real-time, just end the parent
            self.run_tree.end(
                outputs={
                    "success": self.success,
                    "error": self.error,
                    "duration_ms": self.duration_ms,
                    "total_cost_usd": self.total_cost_usd,
                    "outputs_count": len(self.outputs),
                    "child_runs_count": len(self.child_runs)
                }
            )
            self.run_tree.patch()
            logger.info(f"🔍 LangSmith trace finalized for session: {self.session_id} ({len(self.child_runs)} child runs)")
        except Exception as e:
            logger.warning(f"⚠️ Failed to finalize trace: {e}")


def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) -> Optional[ClaudeOutput]:
    """Handle a single line from Claude's stream output.
    
    Args:
        line: Raw line from Claude's JSON stream
        session: Optional session to store outputs in
        
    Returns:
        ClaudeOutput object if successfully parsed, None otherwise
    """
    if not line.strip():
        return None

    try:
        event = json.loads(line)
        event_type = event.get('type')
        timestamp = datetime.now().timestamp()
        output = None

        if event_type == 'system':
            logger.info("🚀 SYSTEM INIT")
            logger.info(f"   📁 CWD: {event.get('cwd')}")
            logger.info(f"   🤖 Model: {event.get('model')}")
            logger.info(f"   🛡️ Permission Mode: {event.get('permissionMode')}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="system",
                content={
                    "cwd": event.get('cwd'),
                    "model": event.get('model'),
                    "permissionMode": event.get('permissionMode')
                },
                raw_event=event
            )

        elif event_type == 'assistant':
            msg = event.get('message', {})
            content = msg.get('content', [])

            for item in content:
                if item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"🤖 Claude: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=text,
                        raw_event=event
                    )
                    
                elif item.get('type') == 'tool_use':
                    tool = item.get('name')
                    inp = item.get('input', {})
                    tool_id = item.get('id')  # Capture the tool-call ID

                    tool_emojis = {
                        'Write': '📝',
                        'Bash': '⚡',
                        'Read': '👀',
                        'Edit': '✏️',
                        'MultiEdit': '✂️',
                        'TodoRead': '📋',
                        'TodoWrite': '✅',
                        'WebSearch': '🔍',
                        'WebFetch': '🌐'
                    }

                    emoji = tool_emojis.get(tool, '🔧')

                    if tool == 'Write':
                        logger.info(f"{emoji} Writing: {inp.get('file_path')}")
                    elif tool == 'Bash':
                        logger.info(f"{emoji} Running: {inp.get('command')}")
                    elif tool == 'Read':
                        logger.info(f"{emoji} Reading: {inp.get('file_path')}")
                    elif tool == 'Edit':
                        logger.info(f"{emoji} Editing: {inp.get('file_path')}")
                    else:
                        logger.info(f"{emoji} Tool: {tool}")

                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_call",
                        content={
                            "id": tool_id,  # Include the tool ID for matching with results
                            "name": tool,
                            "input": inp
                        },
                        raw_event=event
                    )

                elif item.get('type') == 'thinking':
                    # Handle Claude's thinking/reasoning blocks
                    thinking_text = item.get('text', '')
                    logger.info(f"🧠 Thinking: {thinking_text[:100]}...")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="thinking",
                        content=thinking_text,
                        raw_event=event
                    )

        elif event_type == 'user':
            msg = event.get('message', {})
            for item in msg.get('content', []):
                if item.get('type') == 'tool_result':
                    tool_use_id = item.get('tool_use_id')
                    is_error = item.get('is_error', False)
                    content = item.get('content', '')
                    
                    if is_error:
                        logger.error(f"   ❌ Error: {content[:100]}...")
                    else:
                        if content:
                            logger.info(f"   ✅ Result: {content[:100]}...")
                        else:
                            logger.info("   ✅ Success")
                    
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={
                            "tool_use_id": tool_use_id,
                            "is_error": is_error,
                            "content": content
                        },
                        raw_event=event
                    )

        elif event_type == 'result':
            is_error = event.get('is_error', False)
            result = event.get('result', '')
            duration_ms = event.get('duration_ms', 0)
            total_cost_usd = event.get('total_cost_usd', 0)
            
            logger.info("🏁 FINAL RESULT:")
            logger.info(f"   📊 {'✅ SUCCESS' if not is_error else '❌ FAILED'}")
            logger.info(f"   📝 {result}")
            logger.info(f"   ⏱️  {duration_ms}ms")
            logger.info(f"   💰 ${total_cost_usd:.4f}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="result",
                content={
                    "is_error": is_error,
                    "result": result,
                    "duration_ms": duration_ms,
                    "total_cost_usd": total_cost_usd
                },
                raw_event=event
            )
            
            # Update session if provided
            if session:
                session.duration_ms = duration_ms
                session.total_cost_usd = total_cost_usd
                session.finalize(success=not is_error, error=result if is_error else None)

        elif event_type == 'error':
            error_msg = event.get('error', event.get('message', 'Unknown error'))
            logger.error(f"💥 Error: {error_msg}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="error",
                content=error_msg,
                raw_event=event
            )
            
            if session:
                session.finalize(success=False, error=error_msg)

        # Add output to session if provided
        if output and session:
            session.add_output(output)
            
        return output

    except json.JSONDecodeError as e:
        logger.debug(f"⚠️ Failed to parse JSON line: {line[:50]}... - {e}")
        return None
    except Exception as e:
        logger.error(f"💥 Unexpected error handling stream: {e}")
        return None


async def run_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    on_output: Optional[Callable[[ClaudeOutput], None]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    enable_tracing: bool = True,
    parent_run: Optional[RunTree] = None,
    session: Optional[ClaudeSession] = None
) -> ClaudeSession:
    """Run Claude Code in the E2B sandbox with the given prompt.

    Args:
        sandbox: The E2B sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI (e.g., --model, --max-turns)
        on_output: Optional callback for each output
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        enable_tracing: Whether to enable LangSmith tracing
        parent_run: Optional parent LangSmith run for nesting
        session: Optional pre-existing ClaudeSession to use

    Returns:
        ClaudeSession object containing all outputs and metadata
    """
    if session is None:
        session_id = f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}"
        session = ClaudeSession(session_id=session_id, prompt=prompt, trace_enabled=enable_tracing)
        # Initialize LangSmith tracing
        if enable_tracing:
            session.init_tracing(parent_run=parent_run)
    else:
        # If a session is passed, keep its existing tracing settings
        session.prompt = prompt
        # Don't override trace_enabled if session already has tracing initialized


    logger.info(f"🤖 Starting Claude Code session: {session.session_id}")
    logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
    
    # Build Claude command
    cmd_parts = ["claude", "-p", "--output-format", "stream-json", "--verbose", "--dangerously-skip-permissions"]
    
    # Add any additional options
    if claude_options:
        for key, value in claude_options.items():
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))
    
    # Add the prompt via echo
    # Escape the prompt for shell
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    full_command = f"echo '{escaped_prompt}' | {' '.join(cmd_parts)}"
    
    logger.debug(f"🔧 Full command: {full_command[:200]}...")
    
    try:
        # Run the command with streaming
        logger.info("⚡ Executing Claude Code...")
        
        async def handle_stdout(data: str):
            """Handle stdout data from the command."""
            print(f"DEBUG: handle_stdout called with {len(data)} chars")
            for line in data.strip().split('\n'):
                if line:
                    output = handle_claude_stream(line, session)
                    if output and on_output:
                        if asyncio.iscoroutinefunction(on_output):
                            await on_output(output)
                        else:
                            on_output(output)
        
        async def handle_stderr(data: str):
            """Handle stderr data from the command."""
            if data.strip():
                logger.warning(f"⚠️ STDERR: {data}")
                print(f"--- CLAUDE CLI STDERR --- \n{data}\n--------------------------")
        
        # Execute with streaming
        result = await sandbox.commands.run(
            full_command,
            on_stdout=handle_stdout,
            on_stderr=handle_stderr,
            cwd=cwd or "/home/<USER>/workspace",
            timeout=timeout
        )
        
        if result.exit_code != 0:
            error_msg = f"Claude command exited with code {result.exit_code}"
            logger.error(f"❌ {error_msg}")
            session.finalize(success=False, error=error_msg)
        else:
            logger.info(f"✅ Claude Code session completed successfully")
            if not session.end_time:  # If not already finalized by result event
                session.finalize(success=True)
        
    except asyncio.TimeoutError:
        error_msg = f"Claude command timed out after {timeout} seconds"
        logger.error(f"⏱️ {error_msg}")
        session.finalize(success=False, error=error_msg)
    except Exception as e:
        error_msg = f"Failed to run Claude: {e}"
        logger.error(f"💥 {error_msg}")
        session.finalize(success=False, error=error_msg)
    
    logger.info(f"📊 Session summary:")
    logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
    if session.total_cost_usd:
        logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")
    
    return session


async def stream_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    enable_tracing: bool = True
) -> AsyncIterator[ClaudeOutput]:
    """Stream Claude Code outputs as they arrive.

    This is a generator version that yields outputs in real-time.

    Args:
        sandbox: The E2B sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        enable_tracing: Whether to enable LangSmith tracing

    Yields:
        ClaudeOutput objects as they are parsed from the stream
    """
    outputs_queue = asyncio.Queue()
    session = ClaudeSession(
        session_id=f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}",
        prompt=prompt,
        trace_enabled=enable_tracing
    )

    # Initialize LangSmith tracing
    if enable_tracing:
        session.init_tracing()
    
    async def output_callback(output: ClaudeOutput):
        await outputs_queue.put(output)
    
    # Run Claude in a background task
    async def run_claude_task():
        try:
            await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                on_output=output_callback,
                cwd=cwd,
                timeout=timeout,
                enable_tracing=enable_tracing,  # Pass through the enable_tracing flag
                session=session  # Pass the session we already created with tracing
            )
        finally:
            # Signal completion
            await outputs_queue.put(None)
    
    # Start the Claude task
    task = asyncio.create_task(run_claude_task())
    
    try:
        # Yield outputs as they arrive
        while True:
            output = await outputs_queue.get()
            if output is None:  # End signal
                break
            yield output
    finally:
        # Ensure task is complete
        await task
