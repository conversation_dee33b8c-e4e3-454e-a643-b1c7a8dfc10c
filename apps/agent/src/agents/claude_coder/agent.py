"""Claude-Coder Agent implementation."""

import logging
from typing import Any, Dict, Optional

from agents.base import BaseAgent
from agents.claude_coder.graph import ClaudeCoderGraph
from agents.claude_coder.states import Claude<PERSON>oderState
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.tools import BaseTool
from langchain_core.runnables.config import RunnableConfig

logger = logging.getLogger(__name__)


class ClaudeCoderAgent(BaseAgent):
    """Agent that orchestrates Claude Code through development phases."""
    
    def __init__(
        self,
        model_provider: str = "anthropic",
        model_name: str = "claude-sonnet-4-20250514",
        use_sandbox: bool = False  # We manage our own E2B sandbox
    ):
        """Initialize Claude-Coder agent.
        
        Args:
            model_provider: LLM provider (not used, Claude Code manages its own models)
            model_name: Model name (not used, Claude Code manages its own models)
            use_sandbox: Set to False since we manage E2B sandbox ourselves
        """
        super().__init__(
            model_provider=model_provider,
            model_name=model_name,
            use_sandbox=use_sandbox
        )
        self.graph = ClaudeCoderGraph().compile()
    
    def get_initial_state(self, **kwargs) -> ClaudeCoderState:
        """Build initial state for the Claude-Coder pipeline.
        
        Args:
            **kwargs: Additional arguments:
                - branch_name: Git branch to work on (default: current branch)
                - base_branch: Base branch for PR (default: "main")
                - repo_path: Repository path (default: auto-detect)
                - claude_options: Options to pass to Claude Code CLI
        
        Returns:
            Initial state for the graph
        """
        return {
            "sandbox": None,
            "branch_name": kwargs.get("branch_name", ""),
            "base_branch": kwargs.get("base_branch", "main"),
            "repo_path": kwargs.get("repo_path", ""),
            "current_phase": "",
            "phase_results": {},
            "pr_url": None,
            "error": None,
            "claude_options": kwargs.get("claude_options", {"max-turns": "10"})
        }
    
    async def _execute(self, query: str, config: Optional[RunnableConfig] = None) -> str:
        """Internal execute method required by BaseAgent.
        
        Args:
            query: Task description (ignored - runs on current branch)
            config: Runtime configuration (ignored)
            
        Returns:
            String summary of the pipeline execution
        """
        logger.info("🚀 Starting Claude-Coder pipeline...")
        logger.info(f"📝 Query ignored - running on current branch")
        
        # Get initial state
        initial_state = self.get_initial_state()
        
        try:
            # Use LangGraph's built-in ainvoke
            result = await self.graph.ainvoke(initial_state)
            
            # Extract results
            phase_results = result.get("phase_results", {})
            pr_url = result.get("pr_url")
            error = result.get("error")
            
            # Log summary
            logger.info("📊 Pipeline Summary:")
            for phase, session in phase_results.items():
                if session:
                    status = "✅ Success" if session.success else "❌ Failed"
                    cost = f"${session.total_cost_usd:.4f}" if session.total_cost_usd else "N/A"
                    logger.info(f"   {phase.upper()}: {status} (Cost: {cost})")
            
            if pr_url:
                logger.info(f"📌 PR: {pr_url}")
                return f"Pipeline completed successfully! PR: {pr_url}"
            elif error:
                logger.error(f"❌ Pipeline failed: {error}")
                return f"Pipeline failed: {error}"
            else:
                logger.info("✅ Pipeline completed successfully!")
                return "Pipeline completed successfully!"
                
        except Exception as e:
            logger.error(f"❌ Pipeline error: {e}")
            return f"Pipeline error: {str(e)}"
    
    async def execute(self, task: str, max_steps: int = 10) -> str:
        """Public execute method for convenience.
        
        Args:
            task: Task description (ignored)
            max_steps: Maximum steps (ignored)
            
        Returns:
            String summary of the pipeline execution
        """
        return await self._execute(task)