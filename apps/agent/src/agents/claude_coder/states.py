"""State definitions for Claude-Coder graph."""

from typing import Optional, Dict, Any
from typing_extensions import TypedDict
from e2b_code_interpreter import AsyncSandbox
from agents.claude_e2b.claude import ClaudeSession


class ClaudeCoderState(TypedDict):
    """State for the Claude-Coder graph execution."""
    
    # Sandbox management
    sandbox: Optional[AsyncSandbox]
    
    # Git configuration
    branch_name: str  # Current working branch
    base_branch: str  # Base branch for PR (default: "main")
    repo_path: str  # Repository root path
    
    # Current execution phase
    current_phase: str  # modularize/build/test/doc
    
    # Results from each phase
    phase_results: Dict[str, ClaudeSession]  # Results from each Claude Code execution
    
    # PR management
    pr_url: Optional[str]  # PR URL once created/updated
    
    # Error tracking
    error: Optional[str]
    
    # Additional configuration
    claude_options: Dict[str, Any]  # Options to pass to Claude Code CLI