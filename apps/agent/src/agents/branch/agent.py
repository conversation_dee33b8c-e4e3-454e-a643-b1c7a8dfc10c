from typing import Optional
from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from agents.base import BaseAgent
from agents.branch.graph import BranchGraph
from agents.tools import sandbox_tools, github_tools
from agents.scanner.states import ScanType


class BranchAgent(BaseAgent):
    """Branch agent that combines scanner and coder functionality."""
    
    def __init__(self):
        super().__init__()
        
        # Combine sandbox tools and GitHub tools
        tools = sandbox_tools + github_tools
        
        # Create the branch graph
        self.graph = BranchGraph(
            llm=self.llm,
            tools=tools
        ).compile()

    async def _execute(self, query: str, config: Optional[RunnableConfig] = None) -> str:
        """
        Execute the BranchAgent logic (not used - use graph.astream directly).
        
        Use the graph directly:
        agent.graph.astream({
            "scan_type": ScanType.SECURITY,
            "testing": True,
            "repo_id": "990171565"
        })
        """
        # This method exists for BaseAgent compatibility but isn't used
        return "Use graph.astream() directly with scan_type and testing parameters"