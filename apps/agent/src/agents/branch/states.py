from typing import Annotated, Optional
from typing_extensions import TypedDict
from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from agents.scanner.states import ScanType

class BranchState(TypedDict):
    """State for the branch agent that combines scanner and coder."""
    messages: Annotated[list[AnyMessage], add_messages]
    scan_type: ScanType  # Required - no more manual queries
    testing: bool  # Fast testing mode for development
    repo_id: str
    sandbox_session_id: int | None
    db_issue_id: str | None
    scan_results: Optional[str]  # Results from scanner to pass to coder