from agents.branch.agent import BranchAgent
from agents.scanner.states import ScanType

# Get the compiled graph for deployment
agent = BranchAgent().graph  # returns CompiledGraph

# Example usage - SIMPLIFIED API (no manual queries needed):
# 
# For security scan + automated fixes:
# async for chunk in agent.astream({
#     "scan_type": ScanType.SECURITY,
#     "testing": False,
#     "repo_id": "990171565"
# }):
#     print(chunk)
#
# For FAST testing mode (single file, quick analysis):
# async for chunk in agent.astream({
#     "scan_type": ScanType.SECURITY,
#     "testing": True,  # ⚡ SPEED MODE - one file, one action, fast results
#     "repo_id": "990171565"
# }):
#     print(chunk)