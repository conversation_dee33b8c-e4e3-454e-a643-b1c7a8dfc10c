"""Claude Code integration utilities for Modal sandbox."""

import json
import logging
import asyncio
import os
from typing import Optional, List, Dict, Any, AsyncIterator, Callable
from dataclasses import dataclass, field
from datetime import datetime

import modal

# <PERSON><PERSON>mith imports for tracing
try:
    from langsmith import Client
    from langsmith.run_trees import RunTree
    LANGSMITH_AVAILABLE = True
except ImportError:
    LANGSMITH_AVAILABLE = False
    Client = None
    RunTree = None

# Configure logger with emoji support
logger = logging.getLogger(__name__)


@dataclass
class ClaudeOutput:
    """Container for storing Claude Code outputs."""
    timestamp: float
    type: str
    content: Any
    raw_event: Optional[Dict[str, Any]] = None
    
    def __str__(self):
        return f"[{self.timestamp:.2f}] {self.type}: {self.content}"


@dataclass
class ClaudeSession:
    """Container for a complete Claude Code session with LangSmith tracing."""
    session_id: str
    prompt: str
    outputs: List[ClaudeOutput] = field(default_factory=list)
    start_time: float = field(default_factory=lambda: datetime.now().timestamp())
    end_time: Optional[float] = None
    total_cost_usd: Optional[float] = None
    duration_ms: Optional[int] = None
    success: bool = False
    error: Optional[str] = None

    # LangSmith tracing fields
    trace_enabled: bool = False
    run_tree: Optional[Any] = field(default=None, init=False)
    child_runs: List[Any] = field(default_factory=list, init=False)
    pending_tools: Dict[str, Any] = field(default_factory=dict, init=False)

    def __post_init__(self):
        """Initialize LangSmith tracing if enabled."""
        if self.trace_enabled:
            self.init_tracing()

    def init_tracing(self, parent_run: Optional[Any] = None):
        """Initialize LangSmith tracing with optional parent run for nesting."""
        if not LANGSMITH_AVAILABLE:
            logger.warning("⚠️ LangSmith not available. Install with: pip install langsmith")
            self.trace_enabled = False
            return

        try:
            # Auto-configure from environment variables
            api_key = os.environ.get("LANGCHAIN_API_KEY")
            project = os.environ.get("LANGCHAIN_PROJECT", "claude-modal-tracing")
            tracing_enabled = os.environ.get("LANGCHAIN_TRACING_V2", "false").lower() == "true"

            if not api_key or not tracing_enabled:
                logger.warning("⚠️ LangSmith tracing disabled. Set LANGCHAIN_API_KEY and LANGCHAIN_TRACING_V2=true")
                self.trace_enabled = False
                return

            # Create the main run tree for this Claude session
            logger.info(f"🔍 Initializing tracing with parent_run: {parent_run.id if parent_run else 'None'}")

            self.run_tree = RunTree(
                name="🤖 Claude Code Session (Modal)",
                run_type="chain",
                inputs={
                    "prompt": self.prompt,
                    "session_id": self.session_id,
                    "sandbox_type": "modal"
                },
                project_name=project,
                extra={
                    "description": "Claude Code execution in Modal sandbox with real-time tracing",
                    "sandbox": "modal"
                },
                parent_run=parent_run
            )
            self.run_tree.post()

            logger.info(f"✅ LangSmith tracing initialized for Modal session: {self.session_id}")
            logger.info(f"   🎯 Parent run: {parent_run.id if parent_run else 'None'}")
            logger.info(f"   🆔 This run: {self.run_tree.id}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LangSmith tracing: {e}")
            self.trace_enabled = False

    def add_output(self, output: ClaudeOutput):
        """Add an output to the session and trace it."""
        self.outputs.append(output)

        # Send to LangSmith trace if enabled
        if self.trace_enabled:
            self._trace_output(output)

    def _trace_output(self, output: ClaudeOutput):
        """Send output to LangSmith trace with hierarchical structure and proper tool-result nesting."""
        if not self.run_tree:
            return

        try:
            # Handle tool calls - create as parent nodes and store for result matching
            if output.type == "tool_call":
                if isinstance(output.content, dict):
                    tool_name = output.content.get("name", "Unknown")
                    tool_id = output.content.get("id")

                    # Create tool call as parent node (keep it open until result arrives)
                    tool_run = self.run_tree.create_child(
                        name=f"🔧 Tool: {tool_name}",
                        run_type="tool",
                        inputs={
                            "tool_name": tool_name,
                            "tool_input": output.content.get("input"),
                            "tool_id": tool_id,
                            "timestamp": output.timestamp
                        },
                        extra={
                            "description": f"Calling {tool_name}",
                            "raw_event": output.raw_event
                        }
                    )
                    tool_run.post()
                    # Note: Don't end() the tool_run here - we'll end it when the result arrives

                    # Store the tool run for later result matching
                    if tool_id:
                        self.pending_tools[tool_id] = tool_run

                    self.child_runs.append(tool_run)
                    logger.info(f"📤 Created tool call: 🔧 Tool: {tool_name} (ID: {tool_id})")
                    return

            # Handle tool results - update the original tool node with the result
            elif output.type == "tool_result":
                if isinstance(output.content, dict):
                    tool_use_id = output.content.get("tool_use_id")
                    is_error = output.content.get("is_error", False)
                    result_content = output.content.get("content")

                    # Find the matching tool call
                    parent_tool = self.pending_tools.get(tool_use_id)

                    if parent_tool:
                        # Update the tool node's outputs with the result (no separate child node)
                        parent_tool.outputs = {
                            "result": result_content,
                            "is_error": is_error,
                            "tool_use_id": tool_use_id,
                            "status": "❌ Error" if is_error else "✅ Success"
                        }

                        # Update the tool name to show completion status
                        tool_name = parent_tool.inputs.get("tool_name", "Unknown")
                        parent_tool.name = f"🔧 Tool: {tool_name} {'❌' if is_error else '✅'}"

                        # End and patch the tool with its result
                        parent_tool.end()
                        parent_tool.patch()

                        # Remove from pending tools
                        del self.pending_tools[tool_use_id]

                        logger.info(f"📤 Updated tool with result: {tool_name} {'❌' if is_error else '✅'} (ID: {tool_use_id})")
                        return
                    else:
                        logger.warning(f"⚠️ No matching tool found for result {tool_use_id}")

            # Handle other output types (system, claude_message, etc.) as before
            run_configs = {
                "system": {
                    "name": "🚀 System Init",
                    "run_type": "chain",
                    "description": "Claude system initialization"
                },
                "claude_message": {
                    "name": "🤖 Claude Message",
                    "run_type": "llm",
                    "description": "Claude's reasoning and response"
                },
                "thinking": {
                    "name": "🧠 Claude Thinking",
                    "run_type": "llm",
                    "description": "Claude's internal reasoning process"
                },
                "error": {
                    "name": "💥 Error",
                    "run_type": "chain",
                    "description": "Error occurred"
                },
                "result": {
                    "name": "📊 Final Result",
                    "run_type": "chain",
                    "description": "Session completion summary"
                }
            }

            config = run_configs.get(output.type, {
                "name": f"📝 {output.type}",
                "run_type": "chain",
                "description": output.type
            })

            # Create more detailed inputs/outputs for better visibility
            inputs = {
                "type": output.type,
                "timestamp": output.timestamp
            }

            outputs = {"content": output.content}

            # Add extra context for specific types
            if output.type == "result":
                if isinstance(output.content, dict):
                    outputs["duration_ms"] = output.content.get("duration_ms")
                    outputs["cost_usd"] = output.content.get("total_cost_usd")
                    outputs["success"] = not output.content.get("is_error")

            # Create child run with enhanced metadata
            child_run = self.run_tree.create_child(
                name=config["name"],
                run_type=config["run_type"],
                inputs=inputs,
                outputs=outputs,
                extra={
                    "description": config["description"],
                    "raw_event": output.raw_event
                }
            )
            child_run.post()

            # For real-time visibility, end and patch the child run immediately
            child_run.end()
            child_run.patch()

            # Still store it for reference
            self.child_runs.append(child_run)

            logger.info(f"📤 Created child run in LangSmith: {config['name']} (Total child runs: {len(self.child_runs)})")

        except Exception as e:
            logger.warning(f"⚠️ Failed to trace output: {e}")

    def finalize(self, success: bool = True, error: Optional[str] = None):
        """Mark the session as complete and finalize tracing."""
        self.end_time = datetime.now().timestamp()
        self.success = success
        self.error = error

        # Calculate final metrics
        if self.end_time and self.start_time:
            self.duration_ms = int((self.end_time - self.start_time) * 1000)

        # Finalize LangSmith trace
        if self.trace_enabled:
            self._finalize_trace()

    def _finalize_trace(self):
        """Finalize the LangSmith trace."""
        if not self.run_tree:
            return

        try:
            # Child runs are already ended in real-time, just end the parent
            self.run_tree.end(
                outputs={
                    "success": self.success,
                    "error": self.error,
                    "duration_ms": self.duration_ms,
                    "total_cost_usd": self.total_cost_usd,
                    "total_outputs": len(self.outputs),
                    "child_runs_created": len(self.child_runs)
                }
            )
            self.run_tree.patch()

            logger.info(f"✅ LangSmith trace finalized for Modal session: {self.session_id}")
            logger.info(f"   📊 Total child runs: {len(self.child_runs)}")
            logger.info(f"   ⏱️ Duration: {self.duration_ms}ms")
            if self.total_cost_usd:
                logger.info(f"   💰 Total cost: ${self.total_cost_usd:.4f}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to finalize LangSmith trace: {e}")

    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds."""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now().timestamp() - self.start_time


def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) -> Optional[ClaudeOutput]:
    """Handle a single line from Claude's stream output.
    
    Args:
        line: Raw line from Claude's JSON stream
        session: Optional session to store outputs in
        
    Returns:
        ClaudeOutput object if successfully parsed, None otherwise
    """
    if not line.strip():
        return None

    try:
        event = json.loads(line)
        event_type = event.get('type')
        timestamp = datetime.now().timestamp()
        output = None

        if event_type == 'system':
            logger.info("🚀 SYSTEM INIT")
            logger.info(f"   📁 CWD: {event.get('cwd')}")
            logger.info(f"   🤖 Model: {event.get('model')}")
            logger.info(f"   🛡️ Permission Mode: {event.get('permissionMode')}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="system",
                content={
                    "cwd": event.get('cwd'),
                    "model": event.get('model'),
                    "permissionMode": event.get('permissionMode')
                },
                raw_event=event
            )

        elif event_type == 'assistant':
            msg = event.get('message', {})
            content = msg.get('content', [])

            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(f"🤖 Claude: {item}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=item,
                        raw_event=event
                    )
                elif isinstance(item, dict) and item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"🤖 Claude: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=text,
                        raw_event=event
                    )
                    
                elif isinstance(item, dict) and item.get('type') == 'tool_use':
                    tool = item.get('name')
                    inp = item.get('input', {})
                    
                    tool_emojis = {
                        'Write': '📝',
                        'Bash': '⚡',
                        'Read': '👀',
                        'Edit': '✏️',
                        'MultiEdit': '✂️',
                        'TodoRead': '📋',
                        'TodoWrite': '✅',
                        'WebSearch': '🔍',
                        'WebFetch': '🌐'
                    }
                    
                    emoji = tool_emojis.get(tool, '🔧')
                    
                    if tool == 'Write':
                        logger.info(f"{emoji} Writing: {inp.get('file_path')}")
                    elif tool == 'Bash':
                        logger.info(f"{emoji} Running: {inp.get('command')}")
                    elif tool == 'Read':
                        logger.info(f"{emoji} Reading: {inp.get('file_path')}")
                    elif tool == 'Edit':
                        logger.info(f"{emoji} Editing: {inp.get('file_path')}")
                    else:
                        logger.info(f"{emoji} Tool: {tool}")
                    
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_call",
                        content={
                            "name": tool,
                            "input": inp
                        },
                        raw_event=event
                    )

        elif event_type == 'user':
            msg = event.get('message', {})
            content = msg.get('content', [])
            
            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(f"👤 User: {item}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="user_message",
                        content=item,
                        raw_event=event
                    )
                elif isinstance(item, dict) and item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"👤 User: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="user_message",
                        content=text,
                        raw_event=event
                    )

        elif event_type == 'tool_result':
            tool_use_id = event.get('tool_use_id')
            content = event.get('content', [])
            
            for item in content:
                # Handle both string and dict formats
                if isinstance(item, str):
                    logger.info(f"🔧 Tool Result: {item[:100]}{'...' if len(item) > 100 else ''}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={
                            "tool_use_id": tool_use_id,
                            "result": item
                        },
                        raw_event=event
                    )
                elif isinstance(item, dict) and item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"🔧 Tool Result: {text[:100]}{'...' if len(text) > 100 else ''}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={
                            "tool_use_id": tool_use_id,
                            "result": text
                        },
                        raw_event=event
                    )

        elif event_type == 'result':
            result = event.get('result', {})
            
            # Handle both string and dict formats for result
            if isinstance(result, str):
                # When result is a string, extract metadata from the event itself
                total_cost_usd = event.get('total_cost_usd')
                duration_ms = event.get('duration_ms')
                is_error = event.get('is_error', False)
                result_content = result
            else:
                # When result is a dict, use the original logic
                total_cost_usd = result.get('total_cost_usd')
                duration_ms = result.get('duration_ms')
                is_error = result.get('error') is not None
                result_content = result.get('error') or result.get('output', 'Success')
            
            logger.info(f"📊 RESULT:")
            if total_cost_usd:
                logger.info(f"   💰 Cost: ${total_cost_usd:.4f}")
            if duration_ms:
                logger.info(f"   ⏱️ Duration: {duration_ms}ms")
            
            if is_error:
                logger.error(f"   ❌ Error: {result_content}")
            else:
                logger.info(f"   ✅ Success")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="result",
                content=result,
                raw_event=event
            )
            
            # Update session if provided
            if session:
                session.duration_ms = duration_ms
                session.total_cost_usd = total_cost_usd
                session.finalize(success=not is_error, error=result_content if is_error else None)

        elif event_type == 'error':
            error_msg = event.get('error', event.get('message', 'Unknown error'))
            logger.error(f"💥 Error: {error_msg}")
            
            output = ClaudeOutput(
                timestamp=timestamp,
                type="error",
                content=error_msg,
                raw_event=event
            )
            
            if session:
                session.finalize(success=False, error=error_msg)

        # Add output to session if provided
        if output and session:
            session.add_output(output)
            
        return output

    except json.JSONDecodeError as e:
        logger.debug(f"⚠️ Failed to parse JSON line: {line[:50]}... - {e}")
        return None
    except Exception as e:
        logger.error(f"💥 Unexpected error handling stream: {e}")
        logger.error(f"💥 Event type: {event.get('type') if 'event' in locals() else 'unknown'}")
        logger.error(f"💥 Line content: {line[:200]}...")
        import traceback
        logger.error(f"💥 Traceback: {traceback.format_exc()}")
        return None


async def run_claude_in_sandbox(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    on_output: Optional[Callable[[ClaudeOutput], None]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    stream: bool = False
) -> ClaudeSession:
    """Run Claude Code in the Modal sandbox with the given prompt.
    
    Args:
        sandbox: The Modal sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI (e.g., --model, --max-turns)
        on_output: Optional callback for each output
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        
    Returns:
        ClaudeSession object containing all outputs and metadata
    """
    session_id = f"modal-{id(sandbox)}-{int(datetime.now().timestamp())}"
    session = ClaudeSession(session_id=session_id, prompt=prompt)
    
    logger.info(f"🤖 Starting Claude Code session: {session_id}")
    logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
    
    # Build Claude command
    # Note: Modal runs as root, so we can't use --dangerously-skip-permissions
    cmd_parts = ["claude", "-p", "--output-format", "stream-json", "--verbose", "--allowedTools", "Edit,Write,MultiEdit,Read,Bash"]
    
    # Add any additional options
    if claude_options:
        # Skip options that are for Modal sandbox, not Claude
        skip_options = {"use_snapshot"}
        for key, value in claude_options.items():
            if key in skip_options:
                continue
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))
    
    # Add the prompt via echo
    # Escape the prompt for shell
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    claude_command = ' '.join(cmd_parts)
    full_command = f"echo '{escaped_prompt}' | {claude_command}"
    
    if cwd:
        full_command = f"cd {cwd} && {full_command}"
    
    logger.debug(f"🔧 Full command: {full_command[:200]}...")
    
    try:
        # Run the command with streaming
        logger.info("⚡ Executing Claude Code...")
        
        process = sandbox.exec("bash", "-c", full_command, timeout=timeout)
        
        if stream:
            # Stream output line by line as Modal provides it
            logger.info("🌊 Streaming output...")
            for line in process.stdout:
                # Process each line as it arrives
                line = line.rstrip('\n')
                if line:
                    output = handle_claude_stream(line, session)
                    if output and on_output:
                        if asyncio.iscoroutinefunction(on_output):
                            await on_output(output)
                        else:
                            on_output(output)
            
            # Also check stderr
            stderr_output = process.stderr.read()
            if stderr_output:
                for line in stderr_output.split('\n'):
                    if line.strip():
                        logger.warning(f"⚠️ STDERR: {line}")
        else:
            # Read all at once for non-streaming mode
            stdout = process.stdout.read()
            stderr = process.stderr.read()
            process.wait()  # Wait for process to complete before checking returncode
            
            if stdout:
                stdout_lines = stdout.split('\n')
                # Process each line from stdout
                for line in stdout_lines:
                    if line.strip():
                        output = handle_claude_stream(line, session)
                        if output and on_output:
                            if asyncio.iscoroutinefunction(on_output):
                                await on_output(output)
                            else:
                                on_output(output)
            
            # Log stderr if present
            if stderr:
                stderr_lines = stderr.split('\n')
                for line in stderr_lines:
                    if line.strip():
                        logger.warning(f"⚠️ STDERR: {line}")
        
        # Check return code
        if process.returncode != 0:
            error_msg = f"Claude command exited with code {process.returncode}"
            logger.error(f"❌ {error_msg}")
            stderr = process.stderr.read() if stream else stderr
            if stderr:
                error_msg += f": {stderr}"
            session.finalize(success=False, error=error_msg)
        else:
            logger.info(f"✅ Claude Code session completed successfully")
            if not session.end_time:  # If not already finalized by result event
                session.finalize(success=True)
        
    except Exception as e:
        error_msg = f"Failed to run Claude: {e}"
        logger.error(f"💥 {error_msg}")
        session.finalize(success=False, error=error_msg)
    
    logger.info(f"📊 Session summary:")
    logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
    if session.total_cost_usd:
        logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")
    
    return session


async def stream_claude_in_sandbox(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    enable_tracing: bool = False,
    parent_run: Optional[Any] = None,
    session: Optional[ClaudeSession] = None
) -> AsyncIterator[ClaudeOutput]:
    """Stream Claude Code outputs as they arrive.

    This is a generator version that yields outputs in real-time.
    Modal supports line-by-line streaming via iterating over stdout.

    Args:
        sandbox: The Modal sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        enable_tracing: Enable LangSmith tracing for this session
        parent_run: Parent run for trace nesting
        session: Optional existing session to use (for tracing consistency)

    Yields:
        ClaudeOutput: Individual outputs from Claude Code execution
    """
    # Use existing session or create new one
    if session is None:
        session_id = f"modal-stream-{id(sandbox)}-{int(datetime.now().timestamp())}"
        session = ClaudeSession(session_id=session_id, prompt=prompt, trace_enabled=enable_tracing)

        # Initialize tracing with parent run if enabled
        if enable_tracing and parent_run:
            session.init_tracing(parent_run=parent_run)
    
    logger.info(f"🌊 Starting Claude Code streaming session: {session_id}")
    
    # Build Claude command (same as run_claude_in_sandbox)
    # Note: Modal runs as root, so we can't use --dangerously-skip-permissions
    cmd_parts = ["claude", "-p", "--output-format", "stream-json", "--verbose", "--allowedTools", "Edit,Write,MultiEdit,Read,Bash"]
    
    if claude_options:
        # Skip options that are for Modal sandbox, not Claude
        skip_options = {"use_snapshot"}
        for key, value in claude_options.items():
            if key in skip_options:
                continue
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))
    
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    claude_command = ' '.join(cmd_parts)
    full_command = f"echo '{escaped_prompt}' | {claude_command}"
    
    if cwd:
        full_command = f"cd {cwd} && {full_command}"
    
    try:
        process = sandbox.exec("bash", "-c", full_command, timeout=timeout)
        
        # Stream output line by line as Modal provides it
        logger.info("🌊 Starting streaming session...")
        line_count = 0
        for line in process.stdout:
            # Process each line as it arrives
            line = line.rstrip('\n')
            line_count += 1
            
            if line:
                logger.debug(f"📝 Received line {line_count}: {line[:100]}{'...' if len(line) > 100 else ''}")
                output = handle_claude_stream(line, session)
                if output:
                    yield output
                    # If this is a result or error, we're done
                    if output.type in ["result", "error"]:
                        logger.info(f"🏁 Stream completed with {output.type}")
                        break
            else:
                logger.debug(f"📝 Received empty line {line_count}")
        
        # Check for errors after streaming completes
        logger.info("🔍 Checking process completion...")
        stderr = process.stderr.read()
        exit_code = process.returncode
        
        if stderr:
            logger.warning(f"⚠️ STDERR output: {stderr}")
            
        if exit_code != 0:
            logger.error(f"❌ Process exited with code {exit_code}")
            # Yield error if command failed
            error_output = ClaudeOutput(
                timestamp=datetime.now().timestamp(),
                type="error",
                content=f"Command failed with exit code {exit_code}: {stderr}"
            )
            yield error_output
        else:
            logger.info(f"✅ Process completed successfully")
        
    except Exception as e:
        logger.error(f"💥 Exception in streaming: {e}")
        # Yield error output
        error_output = ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="error",
            content=f"Failed to run Claude: {e}"
        )
        yield error_output


async def run_claude_with_tracing(
    sandbox: modal.Sandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300,
    enable_tracing: bool = True,
    parent_run: Optional[Any] = None
) -> ClaudeSession:
    """Run Claude Code in Modal sandbox with LangSmith tracing.

    This is a convenience function that collects all outputs and returns a complete session.
    For real-time streaming, use stream_claude_in_sandbox() instead.

    Args:
        sandbox: The Modal sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        enable_tracing: Enable LangSmith tracing for this session

    Returns:
        ClaudeSession: Complete session with all outputs and tracing
    """
    session_id = f"modal-traced-{id(sandbox)}-{int(datetime.now().timestamp())}"
    session = ClaudeSession(session_id=session_id, prompt=prompt, trace_enabled=enable_tracing)

    # Initialize tracing with parent run if enabled
    if enable_tracing and parent_run:
        session.init_tracing(parent_run=parent_run)

    logger.info(f"🚀 Starting Claude Code session with tracing: {session_id}")

    try:
        # Collect all outputs from the streaming function using the same session
        async for _ in stream_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            claude_options=claude_options,
            cwd=cwd,
            timeout=timeout,
            enable_tracing=enable_tracing,
            parent_run=parent_run,  # 🎯 Pass parent_run for proper nesting!
            session=session  # 🎯 Use the same session for tracing consistency!
        ):
            # Outputs are already added to session via the streaming function
            # Just continue collecting them
            pass

        # Finalize the session
        session.finalize(success=True)

        logger.info(f"✅ Claude Code session completed: {session_id}")
        logger.info(f"   📊 Total outputs: {len(session.outputs)}")
        logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
        if session.total_cost_usd:
            logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")

    except Exception as e:
        logger.error(f"💥 Claude Code session failed: {e}")
        session.finalize(success=False, error=str(e))

    return session