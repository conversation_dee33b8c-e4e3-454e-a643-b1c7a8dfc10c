#!/usr/bin/env python3
"""
Simple demo script showing Claude Code with LangSmith tracing integration.

This script demonstrates the enhanced Claude Code integration that captures
stream outputs and sends them to LangSmith for visualization.
"""

import asyncio
import os
import sys
import logging
import json
from datetime import datetime

# Set up environment
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
os.environ["E2B_API_KEY"] = "e2b_e25998ec95f66735566bdaa1b41e82a09fc5fb9f"

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our enhanced Claude module
from agents.claude_e2b.claude import ClaudeSession, ClaudeOutput, handle_claude_stream

def test_claude_stream_parsing():
    """Test parsing of Claude stream outputs with tracing."""
    logger.info("🧪 Testing Claude stream parsing with tracing...")
    
    # Create a session with tracing enabled
    session = ClaudeSession(
        session_id="demo-session-123",
        prompt="List files and show directory contents",
        trace_enabled=True
    )
    
    # Initialize tracing
    session.init_tracing()
    
    # Simulate Claude stream outputs (like what your boss showed you)
    sample_outputs = [
        '{"type":"system","subtype":"init","cwd":"/home/<USER>","session_id":"demo-123","model":"claude-3","tools":["Bash","Read","Write"]}',
        '{"type":"assistant","message":{"content":[{"type":"text","text":"I\'ll help you list the files in the current directory."}]}}',
        '{"type":"assistant","message":{"content":[{"type":"tool_use","id":"tool_1","name":"Bash","input":{"command":"ls -la"}}]}}',
        '{"type":"user","message":{"content":[{"type":"tool_result","tool_use_id":"tool_1","content":"total 8\\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 .\\ndrwxr-xr-x 3 <USER> <GROUP> 4096 Jan 1 12:00 ..\\n-rw-r--r-- 1 <USER> <GROUP>   42 Jan 1 12:00 README.md\\n-rw-r--r-- 1 <USER> <GROUP>  123 Jan 1 12:00 main.py","is_error":false}]}}',
        '{"type":"assistant","message":{"content":[{"type":"text","text":"I can see there are two files in the directory: README.md and main.py. Let me show you the contents of the README file."}]}}',
        '{"type":"assistant","message":{"content":[{"type":"tool_use","id":"tool_2","name":"Read","input":{"file_path":"README.md"}}]}}',
        '{"type":"user","message":{"content":[{"type":"tool_result","tool_use_id":"tool_2","content":"# Demo Project\\n\\nThis is a simple demo project showing file operations.","is_error":false}]}}',
        '{"type":"result","is_error":false,"result":"Successfully listed directory contents and showed README file","duration_ms":2500,"total_cost_usd":0.0045}'
    ]
    
    logger.info(f"📡 Processing {len(sample_outputs)} stream outputs...")
    
    # Process each output through our handler
    for i, output_line in enumerate(sample_outputs):
        logger.info(f"📥 Processing output {i+1}/{len(sample_outputs)}")
        
        # Parse the output
        output = handle_claude_stream(output_line, session)
        
        if output:
            logger.info(f"✅ Parsed: {output.type} - {str(output.content)[:100]}...")
            
            # This will automatically send to LangSmith via session.add_output()
            # which calls session._trace_output() internally
        else:
            logger.warning(f"⚠️ Failed to parse output: {output_line[:100]}...")
    
    # Finalize the session (this will complete the LangSmith trace)
    session.finalize(success=True)
    
    logger.info("📊 Session Results:")
    logger.info(f"   ✅ Success: {session.success}")
    logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs: {len(session.outputs)}")
    
    return session

def test_manual_tracing():
    """Test manual creation of trace outputs."""
    logger.info("🔧 Testing manual trace creation...")
    
    session = ClaudeSession(
        session_id="manual-demo-456",
        prompt="Create a Python script to calculate fibonacci numbers",
        trace_enabled=True
    )
    
    # Initialize tracing
    session.init_tracing()
    
    # Manually create some outputs to show the tree structure
    outputs = [
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="system",
            content={"cwd": "/workspace", "model": "claude-3", "session_id": "manual-demo-456"}
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="claude_message",
            content="I'll create a Python script to calculate Fibonacci numbers for you."
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="tool_call",
            content={"name": "Write", "input": {"file_path": "fibonacci.py", "content": "def fibonacci(n):\\n    if n <= 1:\\n        return n\\n    return fibonacci(n-1) + fibonacci(n-2)"}}
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="tool_result",
            content={"tool_use_id": "write_1", "is_error": False, "content": "File written successfully"}
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="claude_message",
            content="Great! I've created the fibonacci.py file. Now let me test it."
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="tool_call",
            content={"name": "Bash", "input": {"command": "python fibonacci.py"}}
        ),
        ClaudeOutput(
            timestamp=datetime.now().timestamp(),
            type="tool_result",
            content={"tool_use_id": "bash_1", "is_error": False, "content": "Fibonacci script executed successfully"}
        )
    ]
    
    # Add each output (this will trace them to LangSmith)
    for output in outputs:
        session.add_output(output)
        logger.info(f"📤 Traced: {output.type}")
    
    # Finalize
    session.finalize(success=True)
    
    logger.info("📊 Manual Trace Results:")
    logger.info(f"   ✅ Success: {session.success}")
    logger.info(f"   📝 Outputs: {len(session.outputs)}")
    
    return session

def main():
    """Main demo function."""
    logger.info("🎯 Claude Code LangSmith Tracing Demo")
    logger.info("=" * 60)
    
    # Check environment
    api_key = os.getenv("LANGCHAIN_API_KEY")
    if not api_key:
        logger.error("❌ LANGCHAIN_API_KEY not set!")
        return
    
    logger.info("✅ Environment configured:")
    logger.info(f"   🔑 LangSmith API Key: {api_key[:20]}...")
    logger.info(f"   📊 Project: {os.getenv('LANGCHAIN_PROJECT', 'default')}")
    logger.info(f"   🔍 Tracing: {os.getenv('LANGCHAIN_TRACING_V2', 'false')}")
    
    try:
        logger.info("\n" + "=" * 60)
        logger.info("🧪 Test 1: Stream Parsing with Tracing")
        session1 = test_claude_stream_parsing()
        
        logger.info("\n" + "=" * 60)
        logger.info("🔧 Test 2: Manual Trace Creation")
        session2 = test_manual_tracing()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Demo completed successfully!")
        logger.info("🔍 Check your LangSmith dashboard to see the traces:")
        logger.info("   https://smith.langchain.com/")
        logger.info(f"   Project: {os.getenv('LANGCHAIN_PROJECT', 'default')}")
        logger.info("")
        logger.info("📊 You should see:")
        logger.info("   - Tree-like structure showing message flow")
        logger.info("   - User inputs → Claude messages → Tool calls → Tool results")
        logger.info("   - Timing and cost information")
        logger.info("   - Full conversation context")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
