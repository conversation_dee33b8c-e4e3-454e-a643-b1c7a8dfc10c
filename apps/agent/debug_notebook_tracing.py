#!/usr/bin/env python3
"""
Debug why notebook tracing shows black box instead of child nodes
"""

import os
import sys
import logging
from datetime import datetime

# Set up environment
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_tracing_logic():
    """Debug the tracing logic without needing sandbox"""
    logger.info("🔍 DEBUGGING TRACING LOGIC")
    logger.info("=" * 60)

    # Import modules
    sys.path.insert(0, 'src/agents/claude_e2b')
    import claude

    # Test 1: Check <PERSON><PERSON>mith client
    logger.info("🧪 TEST 1: LangSmith client check")
    client = claude.get_langsmith_client()
    logger.info(f"   LangSmith client: {client is not None}")
    if client:
        logger.info(f"   Client type: {type(client)}")

    # Test 2: Direct session creation and tracing
    logger.info("\n🧪 TEST 2: Direct session creation")
    session = claude.ClaudeSession(
        session_id=f"debug-direct-{int(datetime.now().timestamp())}",
        prompt="test prompt",
        trace_enabled=True
    )

    logger.info(f"   Before init_tracing:")
    logger.info(f"     trace_enabled: {session.trace_enabled}")
    logger.info(f"     run_tree: {session.run_tree is not None}")

    session.init_tracing()

    logger.info(f"   After init_tracing:")
    logger.info(f"     trace_enabled: {session.trace_enabled}")
    logger.info(f"     run_tree: {session.run_tree is not None}")
    if session.run_tree:
        logger.info(f"     Run tree ID: {session.run_tree.id}")

    # Test 3: Simulate adding outputs
    logger.info("\n🧪 TEST 3: Simulate adding outputs")

    # Create mock outputs like the ones from Claude CLI
    mock_outputs = [
        claude.ClaudeOutput(
            type="system",
            content={"cwd": "/test", "model": "claude-sonnet-4"},
            timestamp=datetime.now().timestamp(),
            raw_event={"type": "system"}
        ),
        claude.ClaudeOutput(
            type="tool_call",
            content={"name": "bash", "input": "ls"},
            timestamp=datetime.now().timestamp(),
            raw_event={"type": "assistant"}
        ),
        claude.ClaudeOutput(
            type="tool_result",
            content={"content": "file1.txt\nfile2.txt", "is_error": False},
            timestamp=datetime.now().timestamp(),
            raw_event={"type": "user"}
        )
    ]

    for i, output in enumerate(mock_outputs, 1):
        logger.info(f"   Adding output {i}: {output.type}")
        session.add_output(output)
        logger.info(f"     Child runs count: {len(session.child_runs)}")

    # Test 4: Check final state
    logger.info(f"\n🧪 TEST 4: Final session state")
    logger.info(f"   Total outputs: {len(session.outputs)}")
    logger.info(f"   Total child runs: {len(session.child_runs)}")
    logger.info(f"   Trace enabled: {session.trace_enabled}")
    logger.info(f"   Run tree exists: {session.run_tree is not None}")

    # Finalize session
    session.finalize(success=True)
    logger.info("   ✅ Session finalized")

if __name__ == "__main__":
    debug_tracing_logic()
