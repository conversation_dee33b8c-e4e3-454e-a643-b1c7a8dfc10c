#!/usr/bin/env python3
"""
A demo script to showcase the CoderAgent's enhanced, nested tracing in LangSmith.
"""

import asyncio
import os
import logging
from agents.coder.agent import CoderAgent

# --- Setup Logging ---
# This provides clear, color-coded feedback in the terminal.
logging.basicConfig(level=logging.INFO, format='\033[92m%(asctime)s\033[0m - \033[94m%(name)s\033[0m - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# --- Configure <PERSON><PERSON><PERSON> ---
# This ensures that all operations are traced and sent to the correct project.
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "coder-agent-nested-tracing-demo"
# IMPORTANT: Ensure LANGCHAIN_API_KEY is set in your environment before running.


async def main():
    """
    Main function to initialize and run the CoderAgent for the demo.
    """
    logger.info("--- 🚀 Starting Coder Agent Tracing Demo ---")
    
    # 1. Initialize the CoderAgent.
    # We are using the default Anthropic model. The agent is configured to use the
    # sandbox and our enhanced tracing logic by default.
    try:
        coder_agent = CoderAgent(use_sandbox=True)
        logger.info("✅ CoderAgent initialized successfully.")
    except Exception as e:
        logger.error(f"🔥 Failed to initialize CoderAgent: {e}")
        return

    # 2. Define a multi-step prompt.
    # This prompt is designed to make the agent use both reasoning and tools multiple times,
    # which will create a rich, multi-level trace in LangSmith.
    prompt = (
        "First, list all the files in the current directory. "
        "Then, write a short summary of the `README.md` file into a new file called `summary.txt`."
    )
    logger.info(f"📝 Sending prompt to agent: \"{prompt}\"")

    # 3. Run the agent.
    # This will execute the CoderGraph, and all our tracing modifications will be active.
    try:
        final_response = await coder_agent.run(prompt)
        logger.info("✅ Agent execution completed.")
        logger.info(f"📦 Final Response from Agent: {final_response}")
    except Exception as e:
        logger.error(f"🔥 An error occurred during agent execution: {e}")
        return
    finally:
        logger.info("--- 🏁 Coder Agent Tracing Demo Finished ---")
        
        # 4. Provide clear instructions for verification.
        print("\n\n" + "="*60)
        print("✅ DEMO COMPLETE!")
        print("To see the proof, please follow these steps:")
        print(f"1. Go to your LangSmith dashboard.")
        print(f"2. Open the project named: '{os.environ['LANGCHAIN_PROJECT']}'")
        print("3. Click on the latest trace.")
        print("4. Expand the 'reasoner' nodes to see the nested 'claude_code_session' traces.")
        print("="*60)


if __name__ == "__main__":
    # This ensures the async main function is run correctly.
    asyncio.run(main())
