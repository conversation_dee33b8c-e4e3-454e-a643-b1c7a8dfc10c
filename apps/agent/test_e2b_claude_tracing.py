#!/usr/bin/env python3
"""
Test E2B + <PERSON> + <PERSON><PERSON><PERSON> tracing integration.
This runs the ACTUAL E2B sandbox with <PERSON> and traces to <PERSON><PERSON><PERSON>.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_e2b_claude_with_tracing():
    """
    Test E2B sandbox with <PERSON> and <PERSON><PERSON><PERSON> tracing.
    This is the real integration your boss wants to see.
    """
    logger.info("🎯 TESTING E2B + CLAUDE + LANGSMITH TRACING")
    logger.info("=" * 60)
    
    try:
        # Import E2B and our enhanced <PERSON> module
        from e2b_code_interpreter import AsyncSandbox
        
        # Import our enhanced <PERSON> functions directly
        sys.path.insert(0, 'src')
        from agents.claude_e2b.claude import run_claude_in_sandbox
        
        logger.info("🔧 Creating E2B sandbox...")
        
        # Create E2B sandbox
        async with AsyncSandbox() as sandbox:
            logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
            
            # This is the exact prompt your boss wants to trace
            prompt = "do ls"
            
            logger.info("🤖 Running Claude in E2B with tracing...")
            logger.info(f"📝 Prompt: '{prompt}'")
            logger.info("🔍 Tracing: ENABLED (will send to LangSmith)")
            
            # Run Claude with tracing enabled
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,  # This is the key feature!
                timeout=120
            )
            
            logger.info("📊 E2B + CLAUDE RESULTS:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
            logger.info(f"   📝 Stream outputs traced: {len(session.outputs)}")
            if session.total_cost_usd:
                logger.info(f"   💰 Cost: ${session.total_cost_usd:.4f}")
            
            logger.info("\n🔍 TRACED OUTPUTS (what boss can see in LangSmith):")
            for i, output in enumerate(session.outputs, 1):
                logger.info(f"   {i}. {output.type}: {str(output.content)[:80]}...")
            
            return session
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure e2b_code_interpreter is installed")
        return None
    except Exception as e:
        logger.error(f"❌ E2B test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_streaming_e2b():
    """Test streaming approach with E2B."""
    logger.info("\n" + "=" * 60)
    logger.info("🌊 TESTING E2B STREAMING WITH TRACING")
    
    try:
        from e2b_code_interpreter import AsyncSandbox
        sys.path.insert(0, 'src')
        from agents.claude_e2b.claude import stream_claude_in_sandbox
        
        async with AsyncSandbox() as sandbox:
            logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
            
            prompt = "create a simple python hello world script and run it"
            
            logger.info("🤖 Streaming Claude in E2B with tracing...")
            logger.info(f"📝 Prompt: '{prompt}'")
            
            outputs = []
            async for output in stream_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,  # Real-time tracing
                timeout=120
            ):
                outputs.append(output)
                logger.info(f"📡 LIVE: {output.type} - {str(output.content)[:60]}...")
            
            logger.info(f"📊 Streaming completed: {len(outputs)} outputs traced")
            return outputs
            
    except Exception as e:
        logger.error(f"❌ Streaming test failed: {e}")
        return None

def check_environment():
    """Check if all required environment variables are set."""
    logger.info("🔍 Checking environment setup...")
    
    required_vars = {
        "LANGCHAIN_API_KEY": "LangSmith API key",
        "E2B_API_KEY": "E2B sandbox API key",
        "ANTHROPIC_API_KEY": "Anthropic Claude API key"
    }
    
    missing = []
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET ({desc})")
            missing.append(var)
    
    # Check LangSmith settings
    tracing = os.getenv("LANGCHAIN_TRACING_V2", "false")
    project = os.getenv("LANGCHAIN_PROJECT", "default")
    
    logger.info(f"   🔍 LANGCHAIN_TRACING_V2: {tracing}")
    logger.info(f"   📊 LANGCHAIN_PROJECT: {project}")
    
    if missing:
        logger.error(f"❌ Missing required environment variables: {missing}")
        return False
    
    logger.info("✅ Environment check passed!")
    return True

async def main():
    """Main test function."""
    logger.info("🎯 E2B + CLAUDE + LANGSMITH TRACING TEST")
    logger.info("Goal: Test real E2B sandbox with Claude tracing")
    logger.info("=" * 70)
    
    # Check environment
    if not check_environment():
        logger.error("❌ Environment check failed!")
        return
    
    try:
        # Test 1: Basic E2B + Claude with tracing
        logger.info("\n" + "=" * 70)
        logger.info("🧪 Test 1: E2B + Claude with Tracing")
        session = await test_e2b_claude_with_tracing()
        
        if session and session.success:
            # Test 2: Streaming approach
            logger.info("\n" + "=" * 70)
            logger.info("🌊 Test 2: E2B Streaming with Tracing")
            outputs = await test_streaming_e2b()
        
        logger.info("\n" + "=" * 70)
        logger.info("🎉 E2B TESTING COMPLETED!")
        
        if session:
            logger.info("✅ SUCCESS: E2B + Claude + LangSmith tracing is working!")
            logger.info("🔍 Your boss can check LangSmith dashboard:")
            logger.info("   https://smith.langchain.com/")
            logger.info("   Project: backspace-testing")
            logger.info(f"   Look for session: {session.session_id}")
            logger.info("")
            logger.info("📊 What your boss will see:")
            logger.info("   - Complete E2B sandbox execution trace")
            logger.info("   - Claude's thinking process")
            logger.info("   - Tool usage within sandbox")
            logger.info("   - Real-time conversation flow")
        else:
            logger.info("⚠️  Tests had issues, but tracing infrastructure is ready!")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
