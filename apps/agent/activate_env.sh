#!/bin/bash
# Virtual Environment Activation Script for Agents Folder

echo "🔧 Activating virtual environment in agents folder..."

# Check if we're in the right directory
if [ ! -d ".venv" ]; then
    echo "❌ Error: .venv directory not found!"
    echo "Make sure you're in the apps/agent directory"
    exit 1
fi

# Activate the virtual environment
source .venv/bin/activate

# Verify activation
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ Virtual environment activated successfully!"
    echo "📍 Virtual env path: $VIRTUAL_ENV"
    echo "🐍 Python version: $(python --version)"
    echo "📦 Python location: $(which python)"
    echo ""
    echo "🎯 Ready for Claude tracing development!"
    echo ""
    echo "💡 Key packages available:"
    echo "   - langsmith ($(pip show langsmith | grep Version | cut -d' ' -f2))"
    echo "   - e2b-code-interpreter ($(pip show e2b-code-interpreter | grep Version | cut -d' ' -f2))"
    echo "   - anthropic ($(pip show anthropic | grep Version | cut -d' ' -f2))"
    echo ""
    echo "🚀 You can now run:"
    echo "   python live_claude_stream_tracer.py"
    echo "   python isolated_claude_test.py"
    echo "   jupyter notebook test-agent.ipynb"
else
    echo "❌ Failed to activate virtual environment"
    exit 1
fi
