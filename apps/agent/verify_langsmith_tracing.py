#!/usr/bin/env python3
"""
Verify <PERSON>Smith tracing is working with real Claude execution.
This will create actual traces you can see in your <PERSON><PERSON><PERSON> dashboard.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Set up environment from .env file
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_real_claude_tracing():
    """Test with real Claude execution and E2B sandbox."""
    logger.info("🚀 Testing REAL Claude execution with LangSmith tracing...")
    
    try:
        from e2b_code_interpreter import AsyncSandbox
        from agents.claude_e2b.claude import run_claude_in_sandbox
        
        # Create E2B sandbox
        async with AsyncSandbox() as sandbox:
            logger.info(f"📦 Created E2B sandbox: {sandbox.sandbox_id}")
            
            # Simple prompt that will generate traceable outputs
            prompt = "List the files in the current directory and show me the first few lines of any Python files you find"
            
            logger.info("🤖 Running Claude with tracing enabled...")
            logger.info(f"📝 Prompt: {prompt}")
            
            # Run Claude with tracing
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,
                timeout=60
            )
            
            logger.info("📊 Execution Results:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
            logger.info(f"   📝 Outputs captured: {len(session.outputs)}")
            if session.total_cost_usd:
                logger.info(f"   💰 Cost: ${session.total_cost_usd:.4f}")
            
            # Show what was traced
            logger.info("🔍 Traced outputs:")
            for i, output in enumerate(session.outputs, 1):
                logger.info(f"   {i}. {output.type}: {str(output.content)[:80]}...")
            
            return session
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure e2b_code_interpreter is installed")
        return None
    except Exception as e:
        logger.error(f"❌ Execution failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_streaming_tracing():
    """Test streaming with tracing."""
    logger.info("🌊 Testing STREAMING Claude execution with tracing...")
    
    try:
        from e2b_code_interpreter import AsyncSandbox
        from agents.claude_e2b.claude import stream_claude_in_sandbox
        
        async with AsyncSandbox() as sandbox:
            logger.info(f"📦 Created E2B sandbox: {sandbox.sandbox_id}")
            
            prompt = "Create a simple Python hello world script and run it"
            
            logger.info("🤖 Streaming Claude with tracing...")
            logger.info(f"📝 Prompt: {prompt}")
            
            outputs = []
            async for output in stream_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,
                timeout=60
            ):
                outputs.append(output)
                logger.info(f"📡 Streamed: {output.type} - {str(output.content)[:60]}...")
            
            logger.info(f"📊 Streaming completed: {len(outputs)} outputs captured")
            return outputs
            
    except Exception as e:
        logger.error(f"❌ Streaming test failed: {e}")
        return None

def check_environment():
    """Check if all required environment variables are set."""
    logger.info("🔍 Checking environment setup...")
    
    required_vars = {
        "LANGCHAIN_API_KEY": "LangSmith API key",
        "E2B_API_KEY": "E2B sandbox API key",
        "ANTHROPIC_API_KEY": "Anthropic Claude API key"
    }
    
    missing = []
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET ({desc})")
            missing.append(var)
    
    # Check LangSmith settings
    tracing = os.getenv("LANGCHAIN_TRACING_V2", "false")
    project = os.getenv("LANGCHAIN_PROJECT", "default")
    
    logger.info(f"   🔍 LANGCHAIN_TRACING_V2: {tracing}")
    logger.info(f"   📊 LANGCHAIN_PROJECT: {project}")
    
    if missing:
        logger.error(f"❌ Missing required environment variables: {missing}")
        return False
    
    logger.info("✅ Environment check passed!")
    return True

async def main():
    """Main verification function."""
    logger.info("🎯 LangSmith Tracing Verification")
    logger.info("=" * 60)
    
    # Check environment
    if not check_environment():
        logger.error("❌ Environment check failed!")
        return
    
    # Set tracing environment variables
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
    
    logger.info("\n" + "=" * 60)
    logger.info("🧪 Test 1: Real Claude Execution with Tracing")
    session = await test_real_claude_tracing()
    
    if session:
        logger.info("\n" + "=" * 60)
        logger.info("🌊 Test 2: Streaming Execution with Tracing")
        outputs = await test_streaming_tracing()
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 Verification completed!")
    logger.info("")
    logger.info("🔍 To see your traces in LangSmith:")
    logger.info("   1. Go to: https://smith.langchain.com/")
    logger.info("   2. Select project: 'backspace-testing'")
    logger.info("   3. Look for recent traces with names like:")
    logger.info("      - 'claude_code_session'")
    logger.info("      - Session IDs starting with 'e2b-'")
    logger.info("")
    logger.info("📊 You should see:")
    logger.info("   - Tree structure showing conversation flow")
    logger.info("   - System init → Claude messages → Tool calls → Results")
    logger.info("   - Timing and cost information")
    logger.info("   - Full input/output context")

if __name__ == "__main__":
    asyncio.run(main())
