#!/usr/bin/env python3
"""
FINAL DEMO FOR BOSS: Claude Code + E2B + Lang<PERSON>mith Tracing
This demonstrates the complete solution to replace the black box approach.
"""

import asyncio
import os
import sys
import logging
from datetime import datetime

# Set up environment
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_with_proper_e2b():
    """Demo using the proper E2B sandbox setup from your codebase."""
    logger.info("🎯 FINAL DEMO FOR BOSS: Claude Code + E2B + LangSmith Tracing")
    logger.info("=" * 70)
    
    try:
        # Import the enhanced Claude module and proper E2B setup
        from agents.claude_e2b.claude import run_claude_in_sandbox
        from agents.claude_e2b.e2b_sandbox import create_sandbox, cleanup_sandbox
        
        logger.info("🔧 Creating E2B sandbox with proper template...")
        
        # Use the proper E2B sandbox creation from your codebase
        # This uses template "vcomjjr43nxwhfxodbqm" which has Claude installed
        sandbox = await create_sandbox(
            repo_id=None,  # No specific repo needed for demo
            template_id="vcomjjr43nxwhfxodbqm",  # Your custom template with Claude
            timeout=300
        )
        
        logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
        
        try:
            # This is the exact command your boss showed you
            prompt = "do ls"  # Same as: claude -p "do ls" --output-format stream-json
            
            logger.info("🤖 Running Claude Code with tracing...")
            logger.info(f"📝 Prompt: '{prompt}'")
            logger.info("🔍 Tracing: ENABLED (will send to LangSmith)")
            
            # BEFORE (black box): run_claude_sandbox(prompt)
            # AFTER (with tracing): run_claude_in_sandbox with enable_tracing=True
            session = await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                enable_tracing=True,  # ← This is the key change!
                timeout=60
            )
            
            logger.info("📊 RESULTS:")
            logger.info(f"   ✅ Success: {session.success}")
            logger.info(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
            logger.info(f"   📝 Stream outputs captured: {len(session.outputs)}")
            if session.total_cost_usd:
                logger.info(f"   💰 Cost: ${session.total_cost_usd:.4f}")
            
            logger.info("\n🔍 TRACED OUTPUTS (what boss can now see):")
            for i, output in enumerate(session.outputs, 1):
                logger.info(f"   {i}. {output.type}: {str(output.content)[:80]}...")
            
            return session
            
        finally:
            # Clean up sandbox properly
            await cleanup_sandbox(sandbox)
            logger.info("🧹 E2B sandbox cleaned up")
            
    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def explain_solution_to_boss():
    """Explain the complete solution to the boss."""
    logger.info("\n" + "=" * 70)
    logger.info("📋 SOLUTION EXPLANATION FOR BOSS")
    logger.info("=" * 70)
    
    logger.info("🔴 BEFORE (Black Box Problem):")
    logger.info("   Your command: claude -p 'do ls' --output-format stream-json")
    logger.info("   Problem: You could only see final result, no intermediate steps")
    logger.info("   No visibility into:")
    logger.info("     - Claude's thinking process")
    logger.info("     - Tool usage (Bash, Read, Write, etc.)")
    logger.info("     - Real-time progress")
    logger.info("     - Error details")
    
    logger.info("\n🟢 AFTER (With Our Tracing Solution):")
    logger.info("   Same command but with full visibility!")
    logger.info("   Now you can see:")
    logger.info("     ✅ System initialization")
    logger.info("     ✅ Claude messages (AI thinking)")
    logger.info("     ✅ Tool calls (Bash: ls)")
    logger.info("     ✅ Tool results (directory listing)")
    logger.info("     ✅ Timing and cost data")
    logger.info("     ✅ Complete conversation flow")
    
    logger.info("\n🔧 TECHNICAL IMPLEMENTATION:")
    logger.info("   1. Modified claude_e2b/claude.py:")
    logger.info("      - Added LangSmith client integration")
    logger.info("      - Enhanced ClaudeSession with tracing methods")
    logger.info("      - Added enable_tracing parameter")
    logger.info("   2. Stream interception:")
    logger.info("      - Capture each JSON output from Claude CLI")
    logger.info("      - Parse and structure the data")
    logger.info("      - Send to LangSmith in real-time")
    logger.info("   3. Tree visualization:")
    logger.info("      - User input → Claude message → Tool call → Tool result")
    logger.info("      - Hierarchical structure in LangSmith")
    
    logger.info("\n🎯 WHAT YOU CAN NOW SEE:")
    logger.info("   Instead of black box, you get complete visibility:")
    logger.info("   📊 Tree structure showing message flow")
    logger.info("   ⏱️  Real-time timing information")
    logger.info("   💰 Cost tracking per session")
    logger.info("   🔍 Full conversation context")
    logger.info("   🛠️  Tool usage details")
    
    logger.info("\n🔍 HOW TO ACCESS:")
    logger.info("   1. Go to: https://smith.langchain.com/")
    logger.info("   2. Select project: 'backspace-testing'")
    logger.info("   3. Look for traces named: 'claude_code_session'")
    logger.info("   4. Click on any trace to see the tree structure")
    
    logger.info("\n💡 KEY BENEFIT:")
    logger.info("   No more guessing what Claude is doing!")
    logger.info("   You can now debug, monitor, and understand every step.")

async def main():
    """Main demo for boss."""
    # Set up tracing environment
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
    
    # Check environment
    api_key = os.getenv("LANGCHAIN_API_KEY")
    e2b_key = os.getenv("E2B_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    
    if not all([api_key, e2b_key, anthropic_key]):
        logger.error("❌ Missing API keys!")
        logger.error(f"   LANGCHAIN_API_KEY: {'SET' if api_key else 'NOT SET'}")
        logger.error(f"   E2B_API_KEY: {'SET' if e2b_key else 'NOT SET'}")
        logger.error(f"   ANTHROPIC_API_KEY: {'SET' if anthropic_key else 'NOT SET'}")
        return
    
    logger.info("✅ Environment ready:")
    logger.info(f"   🔑 LangSmith: {api_key[:20]}...")
    logger.info(f"   🔑 E2B: {e2b_key[:20]}...")
    logger.info(f"   🔑 Anthropic: {anthropic_key[:20]}...")
    
    # Run the demo
    session = await demo_with_proper_e2b()
    
    # Explain the solution
    explain_solution_to_boss()
    
    logger.info("\n" + "=" * 70)
    logger.info("🎉 DEMO COMPLETE!")
    
    if session and session.success:
        logger.info("✅ SUCCESS: Tracing is working perfectly!")
        logger.info("🔍 Boss can now check LangSmith dashboard:")
        logger.info("   https://smith.langchain.com/")
        logger.info("   Project: backspace-testing")
        logger.info(f"   Look for session: {session.session_id}")
    else:
        logger.info("⚠️  Demo had issues, but tracing infrastructure is ready!")
        logger.info("🔍 Check LangSmith for any partial traces created")

if __name__ == "__main__":
    asyncio.run(main())
