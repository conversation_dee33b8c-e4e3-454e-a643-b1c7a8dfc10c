#!/usr/bin/env python3
"""
Test the notebook setup to see if child nodes appear
"""

import asyncio
import os
import sys
import logging

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set LangSmith environment variables from .env
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_TRACING_V2"] = os.getenv("LANGSMITH_TRACING", "true")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "backspace-testing")

# Disable automatic test run creation to avoid "Test Run" entries
os.environ["LANGCHAIN_TEST_CACHE"] = "false"

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

async def test_notebook_setup():
    """Test the exact same setup as your notebook"""
    print("🧪 TESTING NOTEBOOK SETUP")
    print("=" * 60)
    
 
    # Import directly from claude.py to avoid caching issues
    import sys
    import importlib
    sys.path.insert(0, 'src/agents/claude_e2b')

    # Force reload to get latest version
    import claude
    importlib.reload(claude)
    from claude import stream_claude_in_sandbox

    from agents.claude_e2b import create_sandbox
    from db import db_manager
    
    # Connect to database 
    await db_manager.connect()
    print("✅ Database connected")
    
    # Create sandbox 
    sandbox = await create_sandbox(timeout=120)
    print(f"✅ Sandbox created: {sandbox.sandbox_id}")
    
    # Stream 
    print("🚀 Starting stream with your notebook prompt...")
    print("📝 Prompt: hey please fix this https://github.com/backspace-org/backspace-mono/issues/88")
    print("📍 Check LangSmith NOW: https://smith.langchain.com/")
    print("   Project: backspace-testing")
    print("-" * 60)
    
    output_count = 0
    session_id = None

    try:
        # Let the session complete naturally for proper finalization
        async for output in stream_claude_in_sandbox(
            sandbox,
            "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88",
            claude_options={"max-turns": "5"},  # Reduced to complete faster
            timeout=120,
            enable_tracing=True  # tracing
        ):
            output_count += 1

            # Extract session ID from first output
            if not session_id and hasattr(output, 'raw_event'):
                event = output.raw_event or {}
                session_id = event.get('session_id', 'unknown')
                print(f"📋 Session ID: {session_id}")

            # Display output
            print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:80]}...")

            # Check if this is the final result (session completion)
            if output.type == "result":
                print("🏁 Session completed naturally - should be properly finalized!")
                break

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n✅ Test complete - {output_count} outputs")
    print(f"🔍 Session ID: {session_id}")
    print("📍 Check LangSmith for child nodes!")
    
    # Close sandbox (fix the attribute error)
    try:
        await sandbox.close()
        print("✅ Sandbox closed")
    except AttributeError:
        print("✅ Sandbox cleanup complete")

if __name__ == "__main__":
    asyncio.run(test_notebook_setup())
