#!/usr/bin/env python3
"""
Command: claude -p "do ls" --output-format stream-json --verbose --dangerously-skip-permissions
"""

import asyncio
import os
import sys
import logging
import json
import subprocess
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def trace_exact_claude_cli_command():
    """
   Run this command
    claude -p "do ls" --output-format stream-json --verbose --dangerously-skip-permissions
    """
    logger.info("🎯 TRACING EXACT CLAUDE CLI COMMAND")
    logger.info("Command: claude -p \"do ls\" --output-format stream-json --verbose --dangerously-skip-permissions")
    logger.info("=" * 80)
    
    # Import our enhanced Claude tracing
    sys.path.insert(0, 'src/agents/claude_e2b')
    import claude
    
    # Create session for the exact command
    session = claude.ClaudeSession(
        session_id=f"claude-cli-{int(datetime.now().timestamp())}",
        prompt="do ls",
        trace_enabled=True
    )
    
    # Initialize LangSmith tracing
    session.init_tracing()
    logger.info(f"🔍 LangSmith tracing initialized: {session.session_id}")

    claude_command = [
        "claude", 
        "-p", "do ls",
        "--output-format", "stream-json",
        "--verbose",
        "--dangerously-skip-permissions"
    ]
    
    logger.info(f"🚀 Executing EXACT command: {' '.join(claude_command)}")
    
    try:
        
        process = await asyncio.create_subprocess_exec(
            *claude_command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        logger.info("📡 Capturing and tracing Claude CLI stream outputs...")
        
        # Read and trace each line of streaming output in real-time
        line_count = 0
        async for line in process.stdout:
            line_count += 1
            line_str = line.decode('utf-8').strip()
            
            if line_str:  # Skip empty lines
                logger.info(f"📥 CLI Stream {line_count}: {line_str[:100]}...")
                
                # Parse and trace each real output to LangSmith
                output = claude.handle_claude_stream(line_str, session)
                
                if output:
                    logger.info(f"✅ Traced to LangSmith: {output.type}")
                    
                    # Show what will appear in LangSmith tree
                    if output.type == "system":
                        logger.info("   🌳 LangSmith Node: 🚀 System Init")
                    elif output.type == "claude_message":
                        logger.info("   🌳 LangSmith Node: 🤖 Claude Message")
                    elif output.type == "tool_call":
                        tool_name = output.content.get("name", "Unknown")
                        logger.info(f"   🌳 LangSmith Node: 🔧 Tool: {tool_name}")
                    elif output.type == "tool_result":
                        logger.info("   🌳 LangSmith Node: ✅ Tool Result")
                    elif output.type == "result":
                        logger.info("   🌳 LangSmith Node: 🏁 Final Result")
                else:
                    logger.warning(f"⚠️ Failed to parse CLI output: {line_str[:100]}...")
        
        # Wait for process to complete
        await process.wait()
        
        # Finalize the session
        if process.returncode == 0:
            logger.info("✅ Claude CLI command completed successfully")
            session.finalize(success=True)
        else:
            stderr_output = await process.stderr.read()
            error_msg = stderr_output.decode('utf-8')
            logger.error(f"❌ Claude CLI command failed: {error_msg}")
            session.finalize(success=False, error=error_msg)
        
        logger.info("\n" + "=" * 80)
        logger.info("🎉 EXACT CLAUDE CLI TRACING COMPLETED!")
        logger.info(f"📊 Session: {session.session_id}")
        logger.info(f"📝 CLI outputs traced: {len(session.outputs)}")
        logger.info(f"⏱️ Duration: {session.elapsed_time:.2f}s")
        logger.info(f"🔄 Return code: {process.returncode}")
        
        logger.info("\n🔍 CHECK LANGSMITH DASHBOARD:")
        logger.info("   URL: https://smith.langchain.com/")
        logger.info("   Project: backspace-testing")
        logger.info(f"   Session: {session.session_id}")
        logger.info("   Look for: claude_code_session")
        
        logger.info("\n🌳 TREE STRUCTURE YOUR BOSS WILL SEE:")
        for i, output in enumerate(session.outputs, 1):
            if output.type == "system":
                logger.info(f"   {i}. 🚀 System Init")
            elif output.type == "claude_message":
                logger.info(f"   {i}. 🤖 Claude Message")
            elif output.type == "tool_call":
                tool_name = output.content.get("name", "Unknown")
                logger.info(f"   {i}. 🔧 Tool: {tool_name}")
            elif output.type == "tool_result":
                logger.info(f"   {i}. ✅ Tool Result")
            elif output.type == "result":
                logger.info(f"   {i}. 🏁 Final Result")
        
        return session
        
    except FileNotFoundError:
        logger.error("❌ 'claude' command not found!")
        logger.error("Make sure Claude CLI is installed and in PATH")
        session.finalize(success=False, error="Claude CLI not found")
        return session
    except Exception as e:
        logger.error(f"❌ Error running Claude CLI command: {e}")
        session.finalize(success=False, error=str(e))
        return session

def check_environment():
    """Check if all required environment variables are set."""
    logger.info("🔍 Checking environment setup...")
    
    required_vars = {
        "LANGCHAIN_API_KEY": "LangSmith API key",
        "ANTHROPIC_API_KEY": "Anthropic Claude API key"
    }
    
    missing = []
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET ({desc})")
            missing.append(var)
    
    if missing:
        logger.error(f"❌ Missing required environment variables: {missing}")
        return False
    
    logger.info("✅ Environment check passed!")
    return True

async def main():
    """Main function to run exact Claude CLI tracing."""
    logger.info("🎯 EXACT CLAUDE CLI COMMAND TRACER")
    logger.info("Goal: Trace the EXACT command your boss specified")
    logger.info("Command: claude -p \"do ls\" --output-format stream-json --verbose --dangerously-skip-permissions")
    logger.info("=" * 80)
    
    # Check environment
    if not check_environment():
        logger.error("❌ Environment check failed!")
        return
    
    try:
        session = await trace_exact_claude_cli_command()
        
        if session:
            logger.info("\n" + "=" * 80)
     
            logger.info("   - LangSmith dashboard: https://smith.langchain.com/")
            logger.info("   - Project: backspace-testing")
            logger.info(f"   - Session: {session.session_id}")
            logger.info("   - Look for: claude_code_session with tree structure")
        else:
            logger.error("❌ Claude CLI tracing failed")
        
    except Exception as e:
        logger.error(f"❌ Main execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
