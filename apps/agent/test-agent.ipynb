import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

import sys
import os

# Add the src directory to Python path
sys.path.insert(0, '/Users/<USER>/backspace-mono/apps/agent/src')

# Change working directory to the agent folder
os.chdir('/Users/<USER>/backspace-mono/apps/agent')

print("✅ Python path updated")
print(f"✅ Working directory: {os.getcwd()}")

from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import Claude<PERSON>oderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")


import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()

import asyncio
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO)



from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/backspace/backspace-monorepo",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
result = await graph.ainvoke(initial_state)
print(f"Result: {result}")

# Cell 1: Setup Python path and environment
import sys
import os

# Add the src directory to Python path
sys.path.insert(0, '/Users/<USER>/backspace-mono/apps/agent/src')

# Change working directory to the agent folder
os.chdir('/Users/<USER>/backspace-mono/apps/agent')

print("✅ Python path updated")
print(f"✅ Working directory: {os.getcwd()}")

# Cell 2: Load environment variables and enable tracing
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('src/agents/.env')

# Set LangSmith tracing (note: LangSmith client expects LANGCHAIN_* variables)
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "")
os.environ["LANGCHAIN_TRACING_V2"] = os.getenv("LANGSMITH_TRACING", "true")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "backspace-testing")

# Disable automatic test run creation to avoid "Test Run" entries
os.environ["LANGCHAIN_TEST_CACHE"] = "false"

print("✅ Environment variables loaded")
print(f"✅ LangSmith API Key: {os.environ.get('LANGCHAIN_API_KEY', 'NOT SET')[:20]}...")
print(f"✅ LangSmith Project: {os.environ.get('LANGCHAIN_PROJECT', 'NOT SET')}")
print("✅ LangSmith tracing enabled")

# Cell 3: Setup logging
import logging

# Enable logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

print("✅ Logging configured")

# Cell 4: Import and setup
from agents.claude_coder.deployment import agent, graph
import asyncio
from db import db_manager

# Connect to database
await db_manager.connect()
print("✅ Database connected")

# 🚀 MODAL SANDBOX TEST
# Test Modal sandbox integration with Claude Coder

from agents.claude_coder.deployment import agent, graph
from agents.claude_coder.states import ClaudeCoderState, SkipPhase, SandboxType
from db import db_manager
import asyncio
import logging
sys.path.insert(0, 'src/agents')
# Enable detailed logging
logging.basicConfig(level=logging.INFO)

# Connect to database
await db_manager.connect()

print("🔧 Testing Modal Sandbox Integration...")
print("=" * 50)

# Modal-specific configuration
modal_state: ClaudeCoderState = {
    "sandbox_type": SandboxType.MODAL,  # 🎯 Use Modal sandbox
    "sandbox": None,
    "sandbox_run_id": None,
    "repo_id": "990850420",  # Required for Modal
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/workspace",  # Modal uses /workspace
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    },
    "skip_phases": {SkipPhase.DOC, SkipPhase.MODULARIZE, SkipPhase.TEST}  # Only run BUILD phase
}

print("🎯 Modal Configuration:")
print(f"  📦 Sandbox Type: {modal_state['sandbox_type'].value}")
print(f"  🌿 Branch: {modal_state['branch_name']}")
print(f"  🏠 Base Branch: {modal_state['base_branch']}")
print(f"  📂 Repo ID: {modal_state['repo_id']}")
print(f"  ⏭️  Skip Phases: {[p.value for p in modal_state['skip_phases']]}")
print(f"  🔧 Claude Options: {modal_state['claude_options']}")
print()

print("🚀 Starting Modal sandbox test...")
try:
    result = await graph.ainvoke(modal_state)
    
    print("✅ Modal test completed!")
    print(f"📊 Final Result:")
    print(f"  🏗️  Current Phase: {result.get('current_phase', 'unknown')}")
    print(f"  🎯 Sandbox Type: {result.get('sandbox_type', 'unknown')}")
    print(f"  ❌ Error: {result.get('error', 'None')}")
    print(f"  🔗 PR URL: {result.get('pr_url', 'None')}")
    print(f"  📈 Phase Results: {list(result.get('phase_results', {}).keys())}")
    
    if result.get('error'):
        print(f"⚠️  Warning: Test completed with error: {result['error']}")
    else:
        print("🎉 Modal sandbox test successful!")
        
except Exception as e:
    print(f"❌ Modal test failed with exception: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Cell 5: Configure and run the agent
from agents.claude_coder.states import ClaudeCoderState

# Your custom configuration
initial_state: ClaudeCoderState = {
    "sandbox": None,
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/Users/<USER>/Documents/projects/backspace/deep-research-code",
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    }
}

print("🎯 Running with your specific branch configuration...")
print(f"📂 Repository: {initial_state['repo_path']}")
print(f"🌿 Branch: {initial_state['branch_name']}")
print(f"🔄 Base branch: {initial_state['base_branch']}")
print(f"🤖 Max turns: {initial_state['claude_options']['max-turns']}")
print("\n📍 Check LangSmith NOW: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("   Look for: Claude Coder execution tree")
print("=" * 60)

# Run the agent
result = await graph.ainvoke(initial_state)

print("\n" + "=" * 60)
print("🎉 AGENT EXECUTION COMPLETE!")
print(f"📊 Result: {result}")
print("\n🔍 CHECK LANGSMITH DASHBOARD:")
print("   URL: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("   Look for: Complete execution tree with all phases")

# 🚀 MODAL SANDBOX TEST
# Test Modal sandbox integration with Claude Coder

from agents.claude_coder.deployment import agent, graph
from agents.claude_coder.states import ClaudeCoderState, SkipPhase, SandboxType
from db import db_manager
import asyncio
import logging
sys.path.insert(0, 'src/agents')
# Enable detailed logging
logging.basicConfig(level=logging.INFO)

# Connect to database
await db_manager.connect()

print("🔧 Testing Modal Sandbox Integration...")
print("=" * 50)

# Modal-specific configuration
modal_state: ClaudeCoderState = {
    "sandbox_type": SandboxType.MODAL,  # 🎯 Use Modal sandbox
    "sandbox": None,
    "sandbox_run_id": None,
    "repo_id": "990850420",  # Required for Modal
    "branch_name": "tawsif/hiring",  # Your branch
    "base_branch": "main",
    "repo_path": "/workspace",  # Modal uses /workspace
    "current_phase": "",
    "phase_results": {},
    "pr_url": None,
    "error": None,
    "claude_options": {
        "max-turns": "100"
    },
    "skip_phases": {SkipPhase.DOC, SkipPhase.MODULARIZE, SkipPhase.TEST}  # Only run BUILD phase
}

print("🎯 Modal Configuration:")
print(f"  📦 Sandbox Type: {modal_state['sandbox_type'].value}")
print(f"  🌿 Branch: {modal_state['branch_name']}")
print(f"  🏠 Base Branch: {modal_state['base_branch']}")
print(f"  📂 Repo ID: {modal_state['repo_id']}")
print(f"  ⏭️  Skip Phases: {[p.value for p in modal_state['skip_phases']]}")
print(f"  🔧 Claude Options: {modal_state['claude_options']}")
print()

print("🚀 Starting Modal sandbox test...")
try:
    result = await graph.ainvoke(modal_state)
    
    print("✅ Modal test completed!")
    print(f"📊 Final Result:")
    print(f"  🏗️  Current Phase: {result.get('current_phase', 'unknown')}")
    print(f"  🎯 Sandbox Type: {result.get('sandbox_type', 'unknown')}")
    print(f"  ❌ Error: {result.get('error', 'None')}")
    print(f"  🔗 PR URL: {result.get('pr_url', 'None')}")
    print(f"  📈 Phase Results: {list(result.get('phase_results', {}).keys())}")
    
    if result.get('error'):
        print(f"⚠️  Warning: Test completed with error: {result['error']}")
    else:
        print("🎉 Modal sandbox test successful!")
        
except Exception as e:
    print(f"❌ Modal test failed with exception: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")