#!/usr/bin/env python3
"""
Isolated test of Claude E2B tracing - imports only the specific module.
"""

import asyncio
import os
import sys
import logging
import json
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Set up tracing
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_isolated_claude_tracing():
    """
    Test Claude tracing by directly importing only the claude.py module.
    """
    logger.info("🎯 ISOLATED CLAUDE TRACING TEST")
    logger.info("=" * 50)
    
    try:
        # Add the specific path to avoid importing the full agents package
        sys.path.insert(0, 'src/agents/claude_e2b')
        
        # Import only the claude module directly
        import claude
        
        logger.info("✅ Successfully imported claude module")
        
        # Create a session with tracing
        session = claude.ClaudeSession(
            session_id=f"isolated-test-{int(datetime.now().timestamp())}",
            prompt="do ls",
            trace_enabled=True
        )
        
        # Initialize LangSmith tracing
        session.init_tracing()
        logger.info("🔍 LangSmith tracing initialized")
        
        # Test stream parsing with sample data
        sample_outputs = [
            '{"type":"system","subtype":"init","cwd":"/tmp","session_id":"test","model":"claude-3","tools":["Bash","Read","Write"]}',
            '{"type":"assistant","message":{"content":[{"type":"text","text":"I\'ll list the files in the directory."}]}}',
            '{"type":"assistant","message":{"content":[{"type":"tool_use","id":"tool_1","name":"Bash","input":{"command":"ls -la"}}]}}',
            '{"type":"user","message":{"content":[{"type":"tool_result","tool_use_id":"tool_1","content":"total 8\\n-rw-r--r-- 1 <USER> <GROUP> 42 Jan 1 12:00 README.md","is_error":false}]}}'
        ]
        
        logger.info(f"📡 Processing {len(sample_outputs)} stream outputs...")
        
        # Process each output
        for i, json_line in enumerate(sample_outputs, 1):
            logger.info(f"📥 Processing output {i}/{len(sample_outputs)}")
            
            # Parse and trace the output
            output = claude.handle_claude_stream(json_line, session)
            
            if output:
                logger.info(f"✅ Traced: {output.type}")
                logger.info(f"   Content: {str(output.content)[:60]}...")
            else:
                logger.warning(f"⚠️ Failed to parse output")
        
        # Finalize the session
        session.finalize(success=True)
        
        logger.info("📊 ISOLATED TEST RESULTS:")
        logger.info(f"   ✅ Success: {session.success}")
        logger.info(f"   📝 Outputs traced: {len(session.outputs)}")
        logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
        logger.info(f"   🆔 Session ID: {session.session_id}")
        
        return session
        
    except Exception as e:
        logger.error(f"❌ Isolated test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_e2b_only():
    """
    Test E2B sandbox creation without Claude integration.
    """
    logger.info("\n" + "=" * 50)
    logger.info("🔧 TESTING E2B SANDBOX ONLY")
    
    try:
        from e2b_code_interpreter import AsyncSandbox
        
        async with AsyncSandbox() as sandbox:
            logger.info(f"✅ E2B sandbox created: {sandbox.sandbox_id}")
            
            # Run a simple command
            result = await sandbox.run_code("print('Hello from E2B!')")
            
            logger.info("📊 E2B Results:")
            logger.info(f"   📝 Output: {result.text}")
            logger.info(f"   ✅ Success: {not result.error}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ E2B test failed: {e}")
        return False

def check_environment():
    """Check environment setup."""
    logger.info("🔍 Checking environment...")
    
    required_vars = ["LANGCHAIN_API_KEY", "E2B_API_KEY", "ANTHROPIC_API_KEY"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var}: {value[:20]}...")
        else:
            logger.error(f"   ❌ {var}: NOT SET")
            return False
    
    logger.info("✅ Environment ready!")
    return True

async def main():
    """Main test function."""
    logger.info("🎯 ISOLATED CLAUDE + E2B TESTING")
    logger.info("Goal: Test core functionality without agent system dependencies")
    logger.info("=" * 60)
    
    if not check_environment():
        return
    
    try:
        # Test 1: Isolated Claude tracing
        session = await test_isolated_claude_tracing()
        
        # Test 2: E2B sandbox only
        e2b_success = await test_e2b_only()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 ISOLATED TESTING COMPLETED!")
        
        if session:
            logger.info("✅ SUCCESS: Claude tracing is working!")
            logger.info("🔍 Check LangSmith dashboard:")
            logger.info("   https://smith.langchain.com/")
            logger.info("   Project: backspace-testing")
            logger.info(f"   Session: {session.session_id}")
            
            logger.info("\n📊 What your boss will see:")
            logger.info("   - Complete Claude conversation trace")
            logger.info("   - System initialization")
            logger.info("   - Claude messages")
            logger.info("   - Tool calls and results")
            logger.info("   - Tree-like visualization")
        
        if e2b_success:
            logger.info("✅ E2B sandbox is working!")
        
        if session and e2b_success:
            logger.info("\n🎯 READY FOR INTEGRATION!")
            logger.info("Both Claude tracing and E2B are working independently.")
            logger.info("The integration should work once OpenAI dependency is resolved.")
        
    except Exception as e:
        logger.error(f"❌ Main test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
