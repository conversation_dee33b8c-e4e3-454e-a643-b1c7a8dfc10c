import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

from agents.claude_e2b import (
    create_sandbox,
    stream_claude_in_sandbox,

)

from db import db_manager

await db_manager.connect()

sandbox = await create_sandbox(timeout=1000)

# Stream Claude outputs in real-time
async for output in stream_claude_in_sandbox(
    sandbox,
    "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88",
    claude_options={"max-turns": "100"},
    timeout=0
):
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    # Special handling for different output types
    if output.type == "claude_message":
        print(f"  🤖 Claude said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
        if output.content.get('input'):
            print(f"      Input: {output.content.get('input')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print("\n=== STREAMING COMPLETE ===")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

import os

# LangSmith creds / project
os.environ["LANGCHAIN_API_KEY"]   = "***************************************************"          # ← your key
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"]    = "backspace-testing"  

from db import db_manager
from agents.claude_e2b import create_sandbox

await db_manager.connect()
sandbox = await create_sandbox(timeout=1000)

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')

# Import directly from the claude module
import claude_e2b.claude as claude_module
stream_claude_in_sandbox = claude_module.stream_claude_in_sandbox

prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"

async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0,
        enable_tracing=True  # ← NOW THIS WILL CREATE THE TREE!
):
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")
    
print("\n=== STREAM COMPLETE – CHECK LANGSMITH FOR TREE STRUCTURE! ===")



import sys
sys.path.insert(0, 'src/agents')

# Import directly from the claude module
import claude_e2b.claude as claude_module
stream_claude_in_sandbox = claude_module.stream_claude_in_sandbox

# Verify it has enable_tracing
import inspect
sig = inspect.signature(stream_claude_in_sandbox)
print(f"✅ Function signature: {sig}")

prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"

async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0,
        
):
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")
    
print("\n=== STREAM COMPLETE – CHECK LANGSMITH FOR TREE STRUCTURE! ===")

import os
import sys

# Make sure environment is set in the notebook kernel
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true" 
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Test LangSmith connection first
from langsmith import Client
client = Client()
print(f"✅ LangSmith client connected to: {client.api_url}")
print(f"✅ Project: {os.environ['LANGCHAIN_PROJECT']}")

# Now run your Claude code
from agents.claude_e2b import stream_claude_in_sandbox

prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"

print("🚀 Starting Claude session with tracing...")
session_id = None

async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0
):
    if not session_id and hasattr(out, 'raw_event'):
        # Try to extract session ID from the first event
        event = out.raw_event or {}
        session_id = event.get('session_id', 'unknown')
        print(f"📋 Session ID: {session_id}")
    
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")

print(f"\n=== STREAM COMPLETE ===")
print(f"🔍 Check LangSmith for session: {session_id}")
print("📍 URL: https://smith.langchain.com/")
print("📂 Project: backspace-testing")

import os, logging
from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox

# 1. Set all required environment variables
os.environ["LANGCHAIN_API_KEY"]    = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"]    = "backspace-testing"
os.environ["ANTHROPIC_API_KEY"]    = "************************************************************************************************************"

# 2. (Optional) Set up logging to see debug output
import agents.claude_e2b.claude as C
logging.basicConfig(level=logging.INFO, format="%(asctime)s  %(message)s")
C.logger.setLevel(logging.DEBUG)

# 3. Create the sandbox, injecting the Anthropic key
sandbox = await create_sandbox(
    envs={"ANTHROPIC_API_KEY": os.environ["ANTHROPIC_API_KEY"]},
    timeout=120
)

# 4. Run Claude and print every streamed event
prompt = "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88"
print("🚀 Starting Claude session with tracing...")

session_id = None
async for out in stream_claude_in_sandbox(
        sandbox,
        prompt,
        claude_options={"max-turns": "100"},
        timeout=0
):
    if not session_id and hasattr(out, 'raw_event'):
        event = out.raw_event or {}
        session_id = event.get('session_id', 'unknown')
        print(f"📋 Session ID: {session_id}")
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")

print(f"\n=== STREAM COMPLETE ===")
print(f"🔍 Check LangSmith for session: {session_id}")
print("📍 URL: https://smith.langchain.com/")
print("📂 Project: backspace-testing")

# 5. Clean up the sandbox
await sandbox.kill()

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Now import AFTER setting env vars
from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox
from db import db_manager

# Connect to database
await db_manager.connect()

# Create sandbox
sandbox = await create_sandbox(timeout=1000)
print(f"✅ Sandbox created: {sandbox.id}")

# Stream Claude outputs with hierarchical tracing
async for output in stream_claude_in_sandbox(
    sandbox,
    "hey please fix this https://github.com/backspace-org/backspace-mono/issues/88",
    claude_options={"max-turns": "100"},
    timeout=0,
    enable_tracing=True  # ← THIS ENABLES THE TREE STRUCTURE!
):
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    # Special handling for different output types
    if output.type == "claude_message":
        print(f"  🤖 Claude said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
        if output.content.get('input'):
            print(f"      Input: {output.content.get('input')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print("\n=== STREAMING COMPLETE ===")
print("\n🔍 CHECK LANGSMITH FOR TREE STRUCTURE!")
print("   URL: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("   Look for: 🤖 Claude Code Session")

# Cell 1: Set environment variables FIRST
import os
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Set LangSmith env vars BEFORE imports
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Cell 2: Import and setup
import sys
sys.path.insert(0, 'src')

from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox
from db import db_manager

await db_manager.connect()
sandbox = await create_sandbox(timeout=1000)

# Cell 3: Run with tracing
async for output in stream_claude_in_sandbox(
    sandbox,
    "do ls",
    claude_options={"max-turns": "5"},
    timeout=60,
    enable_tracing=True
):
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    if output.type == "claude_message":
        print(f"  🤖 Claude said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print("\n=== STREAMING COMPLETE ===")

# Cell to force reload modules
import importlib
import sys

# Remove cached modules
if 'agents.claude_e2b' in sys.modules:
    del sys.modules['agents.claude_e2b']
if 'agents.claude_e2b.claude' in sys.modules:
    del sys.modules['agents.claude_e2b.claude']

# Now import fresh
from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox

# Cell 1: RESTART YOUR KERNEL FIRST! Then run this cell
import os
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Set LangSmith env vars BEFORE any imports
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Clear any cached modules
modules_to_remove = [
    'agents', 'agents.claude_e2b', 'agents.claude_e2b.claude',
    'agents.claude_e2b.e2b_sandbox', 'db'
]
for module in modules_to_remove:
    if module in sys.modules:
        del sys.modules[module]

print("✅ Environment configured and cache cleared")

# Cell 2: Import modules
sys.path.insert(0, 'src')

from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox
from db import db_manager

# Connect to database
await db_manager.connect()
print("✅ Database connected")

# Create sandbox
sandbox = await create_sandbox(timeout=1000)
print(f"✅ Sandbox created: {sandbox.id}")

# Cell 3: Run with tracing
output_count = 0
async for output in stream_claude_in_sandbox(
    sandbox,
    "do ls",
    claude_options={"max-turns": "5"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:100]}")
    
    if output.type == "claude_message":
        print(f"  🤖 Claude said: {output.content}")
    elif output.type == "tool_call":
        print(f"  🔧 Tool: {output.content.get('name')}")
    elif output.type == "tool_result":
        is_error = output.content.get('is_error', False)
        result_content = output.content.get('content', '')
        print(f"  {'❌' if is_error else '✅'} Tool Result: {result_content[:200]}...")
    elif output.type == "result":
        print(f"  🏁 Final result - Success: {not output.content.get('is_error')}")

print(f"\n=== STREAMING COMPLETE - {output_count} outputs ===")
print("\n🔍 CHECK LANGSMITH: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("   Look for: 🤖 Claude Code Session")

import os

# CRITICAL: Set these BEFORE any imports!
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Also set OpenAI key if needed
os.environ["OPENAI_API_KEY"] = "your-openai-key-here"  # Replace with your actual key

print("✅ Environment variables set")

import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# Load .env file for other variables
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Add src to path
sys.path.insert(0, 'src')

# Import AFTER setting env vars
from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox
from db import db_manager

print("✅ Modules imported")

# Connect to database
await db_manager.connect()
print("✅ Database connected")

# Create sandbox
sandbox = await create_sandbox(timeout=1000)
print(f"✅ Sandbox created: {sandbox.id}")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

#!/usr/bin/env python3
"""
Final notebook code that works - copy this to your Jupyter notebook
"""

print("📝 COPY THESE CELLS TO YOUR JUPYTER NOTEBOOK:")
print("=" * 80)

print("""
# Cell 1: Environment Setup
import os
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

# CRITICAL: Set these BEFORE any imports!
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

print("✅ Environment configured")

# Cell 2: Direct Import (bypass __init__.py issues)
sys.path.insert(0, 'src/agents/claude_e2b')
import claude
import e2b_sandbox

# Fix the init_tracing method
def fixed_init_tracing(self, parent_run=None):
    client = claude.get_langsmith_client()
    if not client or not self.trace_enabled:
        return
    try:
        from langsmith.run_trees import RunTree
        self.run_tree = RunTree(
            name="🤖 Claude Code Session",
            run_type="chain",
            inputs={
                "prompt": self.prompt,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            },
            client=client
        )
        self.run_tree.post()
        print(f"✅ Tracing initialized: {self.run_tree.id}")
    except Exception as e:
        print(f"❌ Tracing error: {e}")
        self.trace_enabled = False

# Apply the fix
claude.ClaudeSession.init_tracing = fixed_init_tracing

# Import the streaming function
stream_claude_in_sandbox = claude.stream_claude_in_sandbox
create_sandbox = e2b_sandbox.create_sandbox

print("✅ Modules imported and patched")

# Cell 3: Create Sandbox
from dotenv import load_dotenv
load_dotenv('src/agents/.env')

# Manual DB connection (simpler)
import os
os.environ["SUPABASE_URI"] = os.getenv("SUPABASE_URI")
os.environ["SUPABASE_SERVICE_ROLE_KEY"] = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

sandbox = await create_sandbox(timeout=1000)
print(f"✅ Sandbox created: {sandbox.id}")

# Cell 4: Stream with Real-Time Tracing
print("🚀 Starting Claude stream...")
print("📍 Check LangSmith NOW: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("-" * 60)

output_count = 0
async for output in stream_claude_in_sandbox(
    sandbox,
    "do ls",
    claude_options={"max-turns": "5"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"[{output.timestamp:.2f}] {output.type}: {str(output.content)[:80]}...")
    
    # You should see nodes appearing in LangSmith in real-time!

print(f"\\n✅ Complete - {output_count} outputs traced")

# Cell 5: Cleanup (optional)
await sandbox.close()
print("✅ Sandbox closed")
""")

print("\n" + "=" * 80)
print("🎯 KEY POINTS:")
print("1. Environment variables MUST be set in Cell 1")
print("2. We bypass the problematic __init__.py by importing directly")
print("3. The monkey patch fixes the trace_id issue")
print("4. Child nodes now appear in LangSmith in REAL-TIME")
print("5. Check LangSmith while the stream is running to see nodes appear")

import os
import sys
import logging
from datetime import datetime

os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"


sys.path.insert(0, 'src/agents/claude_e2b')
import claude
import e2b_sandbox

# Fix the init_tracing method
def fixed_init_tracing(self, parent_run=None):
    client = claude.get_langsmith_client()
    if not client or not self.trace_enabled:
        return
    try:
        from langsmith.run_trees import RunTree
        self.run_tree = RunTree(
            name="🤖 Claude Code Session",
            run_type="chain",
            inputs={
                "prompt": self.prompt,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            },
            client=client
        )
        self.run_tree.post()
        print(f"✅ Tracing initialized: {self.run_tree.id}")
    except Exception as e:
        print(f"❌ Tracing error: {e}")
        self.trace_enabled = False

claude.ClaudeSession.init_tracing = fixed_init_tracing

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")

# Cell 1: Environment Setup (Run this FIRST)
import os
import sys
import logging
from datetime import datetime

# Set ALL environment variables BEFORE imports
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"

print("✅ Environment variables set")

# Cell 2: Import and Apply Fixes
# Import claude module DIRECTLY to apply our fix
sys.path.insert(0, 'src/agents/claude_e2b')
import claude as C
import e2b_sandbox

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s  %(message)s")
C.logger.setLevel(logging.DEBUG)

# Fix the init_tracing method to remove session_name
def fixed_init_tracing(self, parent_run=None):
    client = C.get_langsmith_client()
    if not client or not self.trace_enabled:
        return
    try:
        from langsmith.run_trees import RunTree
        self.run_tree = RunTree(
            name="🤖 Claude Code Session",
            run_type="chain",
            inputs={
                "prompt": self.prompt,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            },
            client=client
        )
        self.run_tree.post()
        C.logger.info(f"🔍 LangSmith tracing initialized for session: {self.session_id}")
    except Exception as e:
        C.logger.warning(f"⚠️ Failed to initialize LangSmith tracing: {e}")
        self.trace_enabled = False

# Apply the fix
C.ClaudeSession.init_tracing = fixed_init_tracing

# Now import the functions
from agents.claude_e2b import create_sandbox, stream_claude_in_sandbox

print("✅ Modules imported and tracing fixed")

# Cell 3: Initialize Database and Create Sandbox
from db import db_manager

# Connect to database
await db_manager.connect()
print("✅ Database connected")

# Create sandbox (this will generate a GitHub token automatically)
sandbox = await create_sandbox(
    envs={"ANTHROPIC_API_KEY": os.environ["ANTHROPIC_API_KEY"]},
    timeout=120
)
print(f"✅ Sandbox created: {sandbox.id}")

# Cell 4: Stream with Hierarchical Tracing
prompt = "do ls"  # Simple test command
print("🚀 Starting Claude session with hierarchical tracing...")
print("📍 Check LangSmith NOW to see nodes appearing in real-time!")
print("   URL: https://smith.langchain.com/")
print("   Project: backspace-testing")
print("-" * 80)

session_id = None
output_count = 0

async for out in stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "5"},  # Reduced for testing
    timeout=60,

):
    output_count += 1
    
    # Extract session ID from first output
    if not session_id and hasattr(out, 'raw_event'):
        event = out.raw_event or {}
        session_id = event.get('session_id', 'unknown')
        print(f"📋 Session ID: {session_id}")
    
    # Display output
    print(f"[{out.timestamp:.2f}] {out.type}: {str(out.content)[:120]}")
    
    # The hierarchical trace is being built in LangSmith in real-time!

print(f"\n=== STREAM COMPLETE ===")
print(f"✅ Total outputs: {output_count}")
print(f"🔍 Check LangSmith for session: {session_id}")
print("📍 URL: https://smith.langchain.com/")
print("📂 Project: backspace-testing")

# Cell 5: Cleanup
await sandbox.close()
await db_manager.disconnect()
print("✅ Sandbox closed and database disconnected")

# Test the fix
import os
os.environ["LANGCHAIN_API_KEY"] = "***************************************************"
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_PROJECT"] = "backspace-testing"

# BYPASS THE PROBLEMATIC __init__.py
import sys
sys.path.insert(0, 'src/agents')
import claude_e2b.claude as claude_module

prompt = "do ls"

print("🔍 Testing FIXED child nodes...")
output_count = 0

async for output in claude_module.stream_claude_in_sandbox(
    sandbox,
    prompt,
    claude_options={"max-turns": "3"},
    timeout=60,
    enable_tracing=True
):
    output_count += 1
    print(f"📤 Output {output_count}: {output.type}")
    
print(f"✅ STREAM COMPLETE - {output_count} outputs")
print("🌳 NOW CHECK LANGSMITH - YOU SHOULD SEE CHILD NODES!")