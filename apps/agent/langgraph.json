{"dockerfile_lines": ["RUN apt-get update && apt-get install -y curl git", "RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg", "RUN echo \"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main\" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null", "RUN apt-get update && apt-get install -y gh", "RUN mkdir -p ~/.local/share/gh/extensions/gh-token", "RUN curl -L -o ~/.local/share/gh/extensions/gh-token/gh-token \"https://github.com/Link-/gh-token/releases/download/v2.0.5/linux-amd64\"", "RUN chmod +x ~/.local/share/gh/extensions/gh-token/gh-token", "RUN echo \"owner: Link-\\nname: gh-token\\nhost: github.com\\ntag: v2.0.5\\nispinned: false\\npath: /root/.local/share/gh/extensions/gh-token/gh-token\" > ~/.local/share/gh/extensions/gh-token/manifest.yml"], "graphs": {"scanner": "./src/agents/scanner/deployment.py:agent", "coder": "./src/agents/coder/deployment.py:agent", "coder_local": "./src/agents/coder/deployment.py:agent_local", "branch": "./src/agents/branch/deployment.py:agent", "claude_coder": "./src/agents/claude_coder/deployment.py:graph"}, "python_version": "3.13", "env": "./.env", "dependencies": ["."], "http": {"app": "./src/webapp.py:app"}}