#!/usr/bin/env python3
"""
Test script for Claude Code LangSmith tracing integration.

This script tests the enhanced Claude Code integration that captures
stream outputs and sends them to LangSmith for visualization.
"""

import asyncio
import os
import pytest
from unittest.mock import Mock, patch
from src.agents.claude_e2b.claude import <PERSON><PERSON><PERSON><PERSON>, ClaudeOutput, handle_claude_stream


def test_claude_session_tracing_init():
    """Test ClaudeSession tracing initialization."""
    session = ClaudeSession(
        session_id="test-session",
        prompt="test prompt",
        trace_enabled=True
    )
    
    assert session.trace_enabled is True
    assert session.run_tree is None  # Not initialized yet
    
    # Test with tracing disabled
    session_no_trace = ClaudeSession(
        session_id="test-session-no-trace",
        prompt="test prompt",
        trace_enabled=False
    )
    
    assert session_no_trace.trace_enabled is False


def test_claude_output_creation():
    """Test ClaudeOutput creation and structure."""
    import time
    timestamp = time.time()
    
    # Test system output
    system_output = ClaudeOutput(
        timestamp=timestamp,
        type="system",
        content={"cwd": "/test", "model": "claude-3"},
        raw_event={"type": "system", "cwd": "/test"}
    )
    
    assert system_output.type == "system"
    assert system_output.content["cwd"] == "/test"
    assert system_output.raw_event is not None
    
    # Test claude message output
    message_output = ClaudeOutput(
        timestamp=timestamp,
        type="claude_message",
        content="Hello, I can help you with that!",
        raw_event={"type": "assistant", "message": {"content": [{"type": "text", "text": "Hello"}]}}
    )
    
    assert message_output.type == "claude_message"
    assert isinstance(message_output.content, str)


def test_handle_claude_stream_parsing():
    """Test parsing of different Claude stream output types."""
    
    # Test system event
    system_line = '{"type":"system","subtype":"init","cwd":"/test","model":"claude-3","session_id":"test"}'
    system_output = handle_claude_stream(system_line)
    
    assert system_output is not None
    assert system_output.type == "system"
    assert system_output.content["cwd"] == "/test"
    
    # Test assistant message
    assistant_line = '{"type":"assistant","message":{"content":[{"type":"text","text":"Hello world"}]}}'
    assistant_output = handle_claude_stream(assistant_line)
    
    assert assistant_output is not None
    assert assistant_output.type == "claude_message"
    assert assistant_output.content == "Hello world"
    
    # Test tool use
    tool_line = '{"type":"assistant","message":{"content":[{"type":"tool_use","name":"Bash","input":{"command":"ls"}}]}}'
    tool_output = handle_claude_stream(tool_line)
    
    assert tool_output is not None
    assert tool_output.type == "tool_call"
    assert tool_output.content["name"] == "Bash"
    assert tool_output.content["input"]["command"] == "ls"
    
    # Test tool result
    result_line = '{"type":"user","message":{"content":[{"type":"tool_result","content":"file1.txt\\nfile2.txt","is_error":false}]}}'
    result_output = handle_claude_stream(result_line)
    
    assert result_output is not None
    assert result_output.type == "tool_result"
    assert result_output.content["is_error"] is False
    
    # Test invalid JSON
    invalid_line = '{"invalid": json'
    invalid_output = handle_claude_stream(invalid_line)
    
    assert invalid_output is None


@patch('src.agents.claude_e2b.claude.langsmith_client')
def test_session_tracing_methods(mock_client):
    """Test ClaudeSession tracing methods."""
    mock_client.return_value = Mock()
    
    session = ClaudeSession(
        session_id="test-session",
        prompt="test prompt",
        trace_enabled=True
    )
    
    # Mock RunTree
    with patch('src.agents.claude_e2b.claude.RunTree') as mock_run_tree:
        mock_tree_instance = Mock()
        mock_run_tree.return_value = mock_tree_instance
        
        # Test tracing initialization
        session.init_tracing()
        
        # Verify RunTree was created and posted
        mock_run_tree.assert_called_once()
        mock_tree_instance.post.assert_called_once()
        
        # Test adding output with tracing
        output = ClaudeOutput(
            timestamp=1234567890,
            type="claude_message",
            content="Test message"
        )
        
        session.run_tree = mock_tree_instance
        session.add_output(output)
        
        # Verify child run was created
        mock_tree_instance.create_child.assert_called_once()
        
        # Test finalization
        session.finalize(success=True)
        
        # Verify trace was ended and patched
        mock_tree_instance.end.assert_called_once()
        mock_tree_instance.patch.assert_called_once()


def test_session_without_langsmith():
    """Test session behavior when LangSmith is not available."""
    # Test with no API key (langsmith_client is None)
    with patch('src.agents.claude_e2b.claude.langsmith_client', None):
        session = ClaudeSession(
            session_id="test-session",
            prompt="test prompt",
            trace_enabled=True
        )
        
        # Should not crash when tracing is unavailable
        session.init_tracing()
        
        output = ClaudeOutput(
            timestamp=1234567890,
            type="claude_message",
            content="Test message"
        )
        
        session.add_output(output)
        session.finalize(success=True)
        
        # Should complete without errors
        assert len(session.outputs) == 1
        assert session.success is True


@pytest.mark.asyncio
async def test_environment_setup():
    """Test environment variable setup for tracing."""
    
    # Test with missing API key
    with patch.dict(os.environ, {}, clear=True):
        from src.agents.claude_e2b.claude import langsmith_client
        # Should be None when no API key is set
        # (This tests the module-level initialization)
    
    # Test with API key present
    with patch.dict(os.environ, {"LANGCHAIN_API_KEY": "test-key"}, clear=True):
        # Re-import to test with environment variable set
        import importlib
        import src.agents.claude_e2b.claude
        importlib.reload(src.agents.claude_e2b.claude)


if __name__ == "__main__":
    # Run basic tests
    print("🧪 Running Claude tracing tests...")
    
    test_claude_session_tracing_init()
    print("✅ Session tracing initialization test passed")
    
    test_claude_output_creation()
    print("✅ Claude output creation test passed")
    
    test_handle_claude_stream_parsing()
    print("✅ Stream parsing test passed")
    
    test_session_without_langsmith()
    print("✅ No LangSmith test passed")
    
    print("🎉 All basic tests passed!")
    print("🔍 Run with pytest for full test suite including async tests")
