diff --git a/apps/agent/src/agents/claude_e2b/claude.py b/apps/agent/src/agents/claude_e2b/claude.py
index ea3918f..deabc23 100644
--- a/apps/agent/src/agents/claude_e2b/claude.py
+++ b/apps/agent/src/agents/claude_e2b/claude.py
@@ -3,15 +3,24 @@
 import json
 import logging
 import asyncio
+import os
 from typing import Optional, List, Dict, Any, AsyncIterator, Callable
 from dataclasses import dataclass, field
 from datetime import datetime
 
 from e2b_code_interpreter import AsyncSandbox
+from langsmith import Client, traceable
+from langsmith.run_trees import RunTree
 
 # Configure logger with emoji support
 logger = logging.getLogger(__name__)
 
+# Initialize LangSmith client
+langsmith_client = Client(
+    api_key=os.getenv("LANGCHAIN_API_KEY"),
+    api_url=os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")
+) if os.getenv("LANGCHAIN_API_KEY") else None
+
 
 @dataclass
 class ClaudeOutput:
@@ -37,17 +46,27 @@ class ClaudeSession:
     duration_ms: Optional[int] = None
     success: bool = False
     error: Optional[str] = None
+    # LangSmith tracing
+    run_tree: Optional['RunTree'] = field(default=None, init=False)
+    trace_enabled: bool = field(default=True)
     
     def add_output(self, output: ClaudeOutput):
         """Add an output to the session."""
         self.outputs.append(output)
-    
+        # Send to LangSmith if tracing is enabled
+        if self.trace_enabled and self.run_tree and langsmith_client:
+            self._trace_output(output)
+
     def finalize(self, success: bool = True, error: Optional[str] = None):
         """Mark the session as complete."""
         self.end_time = datetime.now().timestamp()
         self.success = success
         self.error = error
-        
+
+        # Finalize LangSmith trace
+        if self.trace_enabled and self.run_tree and langsmith_client:
+            self._finalize_trace()
+
     @property
     def elapsed_time(self) -> float:
         """Get elapsed time in seconds."""
@@ -55,6 +74,137 @@ class ClaudeSession:
             return self.end_time - self.start_time
         return datetime.now().timestamp() - self.start_time
 
+    def init_tracing(self):
+        """Initialize LangSmith tracing for this session."""
+        if not langsmith_client or not self.trace_enabled:
+            return
+
+        try:
+            from langsmith.run_trees import RunTree
+
+            self.run_tree = RunTree(
+                name="claude_code_session",
+                run_type="chain",
+                inputs={"prompt": self.prompt, "session_id": self.session_id},
+                session_name=f"claude_session_{self.session_id}",
+                client=langsmith_client
+            )
+            self.run_tree.post()
+            logger.info(f"🔍 LangSmith tracing initialized for session: {self.session_id}")
+        except Exception as e:
+            logger.warning(f"⚠️ Failed to initialize LangSmith tracing: {e}")
+            self.trace_enabled = False
+
+    def _trace_output(self, output: ClaudeOutput):
+        """Send output to LangSmith trace with hierarchical structure."""
+        if not self.run_tree:
+            return
+
+        try:
+            # Define run types and names for better visualization
+            run_configs = {
+                "system": {
+                    "name": "🚀 System Init",
+                    "run_type": "chain",
+                    "description": "Claude system initialization"
+                },
+                "claude_message": {
+                    "name": "🤖 Claude Message", 
+                    "run_type": "llm",
+                    "description": "Claude's reasoning and response"
+                },
+                "thinking": {
+                    "name": "🧠 Claude Thinking",
+                    "run_type": "llm", 
+                    "description": "Claude's internal reasoning process"
+                },
+                "tool_call": {
+                    "name": f"🔧 Tool: {output.content.get('name', 'Unknown')}",
+                    "run_type": "tool",
+                    "description": f"Calling {output.content.get('name', 'tool')}"
+                },
+                "tool_result": {
+                    "name": f"{'✅' if not output.content.get('is_error') else '❌'} Result",
+                    "run_type": "chain",
+                    "description": "Tool execution result"
+                },
+                "error": {
+                    "name": "💥 Error",
+                    "run_type": "chain",
+                    "description": "Error occurred"
+                },
+                "result": {
+                    "name": "📊 Final Result",
+                    "run_type": "chain", 
+                    "description": "Session completion summary"
+                }
+            }
+            
+            config = run_configs.get(output.type, {
+                "name": f"📝 {output.type}",
+                "run_type": "chain",
+                "description": output.type
+            })
+            
+            # Create more detailed inputs/outputs for better visibility
+            inputs = {
+                "type": output.type,
+                "timestamp": output.timestamp
+            }
+            
+            outputs = {"content": output.content}
+            
+            # Add extra context for specific types
+            if output.type == "tool_call":
+                inputs["tool_name"] = output.content.get("name")
+                inputs["tool_input"] = output.content.get("input")
+            elif output.type == "tool_result":
+                outputs["is_error"] = output.content.get("is_error", False)
+                outputs["tool_use_id"] = output.content.get("tool_use_id")
+            elif output.type == "result":
+                outputs["duration_ms"] = output.content.get("duration_ms")
+                outputs["cost_usd"] = output.content.get("total_cost_usd")
+                outputs["success"] = not output.content.get("is_error")
+            
+            # Create child run with enhanced metadata
+            child_run = self.run_tree.create_child(
+                name=config["name"],
+                run_type=config["run_type"],
+                inputs=inputs,
+                outputs=outputs,
+                extra={
+                    "description": config["description"],
+                    "raw_event": output.raw_event
+                }
+            )
+            child_run.post()
+            child_run.end()
+            
+            logger.debug(f"📤 Traced to LangSmith: {config['name']}")
+            
+        except Exception as e:
+            logger.warning(f"⚠️ Failed to trace output: {e}")
+
+    def _finalize_trace(self):
+        """Finalize the LangSmith trace."""
+        if not self.run_tree:
+            return
+
+        try:
+            self.run_tree.end(
+                outputs={
+                    "success": self.success,
+                    "error": self.error,
+                    "duration_ms": self.duration_ms,
+                    "total_cost_usd": self.total_cost_usd,
+                    "outputs_count": len(self.outputs)
+                }
+            )
+            self.run_tree.patch()
+            logger.info(f"🔍 LangSmith trace finalized for session: {self.session_id}")
+        except Exception as e:
+            logger.warning(f"⚠️ Failed to finalize trace: {e}")
+
 
 def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) -> Optional[ClaudeOutput]:
     """Handle a single line from Claude's stream output.
@@ -146,6 +296,17 @@ def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) ->
                         raw_event=event
                     )
 
+                elif item.get('type') == 'thinking':
+                    # Handle Claude's thinking/reasoning blocks
+                    thinking_text = item.get('text', '')
+                    logger.info(f"🧠 Thinking: {thinking_text[:100]}...")
+                    output = ClaudeOutput(
+                        timestamp=timestamp,
+                        type="thinking",
+                        content=thinking_text,
+                        raw_event=event
+                    )
+
         elif event_type == 'user':
             msg = event.get('message', {})
             for item in msg.get('content', []):
@@ -237,10 +398,11 @@ async def run_claude_in_sandbox(
     claude_options: Optional[Dict[str, Any]] = None,
     on_output: Optional[Callable[[ClaudeOutput], None]] = None,
     cwd: Optional[str] = None,
-    timeout: int = 300
+    timeout: int = 300,
+    enable_tracing: bool = True
 ) -> ClaudeSession:
     """Run Claude Code in the E2B sandbox with the given prompt.
-    
+
     Args:
         sandbox: The E2B sandbox instance
         prompt: The prompt to send to Claude
@@ -248,13 +410,18 @@ async def run_claude_in_sandbox(
         on_output: Optional callback for each output
         cwd: Working directory for Claude
         timeout: Command timeout in seconds
-        
+        enable_tracing: Whether to enable LangSmith tracing
+
     Returns:
         ClaudeSession object containing all outputs and metadata
     """
     session_id = f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}"
-    session = ClaudeSession(session_id=session_id, prompt=prompt)
-    
+    session = ClaudeSession(session_id=session_id, prompt=prompt, trace_enabled=enable_tracing)
+
+    # Initialize LangSmith tracing
+    if enable_tracing:
+        session.init_tracing()
+
     logger.info(f"🤖 Starting Claude Code session: {session_id}")
     logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
     
@@ -341,27 +508,34 @@ async def stream_claude_in_sandbox(
     prompt: str,
     claude_options: Optional[Dict[str, Any]] = None,
     cwd: Optional[str] = None,
-    timeout: int = 300
+    timeout: int = 300,
+    enable_tracing: bool = True
 ) -> AsyncIterator[ClaudeOutput]:
     """Stream Claude Code outputs as they arrive.
-    
+
     This is a generator version that yields outputs in real-time.
-    
+
     Args:
         sandbox: The E2B sandbox instance
         prompt: The prompt to send to Claude
         claude_options: Additional options for Claude CLI
         cwd: Working directory for Claude
         timeout: Command timeout in seconds
-        
+        enable_tracing: Whether to enable LangSmith tracing
+
     Yields:
         ClaudeOutput objects as they are parsed from the stream
     """
     outputs_queue = asyncio.Queue()
     session = ClaudeSession(
         session_id=f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}",
-        prompt=prompt
+        prompt=prompt,
+        trace_enabled=enable_tracing
     )
+
+    # Initialize LangSmith tracing
+    if enable_tracing:
+        session.init_tracing()
     
     async def output_callback(output: ClaudeOutput):
         await outputs_queue.put(output)
